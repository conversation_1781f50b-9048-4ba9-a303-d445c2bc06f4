# Scribble 2.0

Scribble is a modern healthcare transcription designed to streamline patient documentation using AI-powered automation. This monorepo contains all the microservices required for the application, including a React frontend, a Node.js Express API, and a FastAPI AI-Core service for handling transcription and EMR generation.

Project Structure

```bash
/Scribble
│── /frontend      # React frontend (User Interface for doctors & admins)
│── /api           # Node.js Express backend (business logic & API gateway)
│── /ai_core       # FastAPI service (AI-powered transcription & EMR processing)
│── /scripts       # Development & deployment scripts
│── README.md      # Project documentation

```

## Services Overview
### Frontend
* Built with React + TypeScript
* Provides a responsive UI for managing patient records & transcriptions
* Connects to the backend API via RESTful endpoints
* Features real-time updates and secure authentication
### Backend API
* Developed using Node.js & Express
* Handles authentication, authorization, and business logic
* Interfaces with AI-Core for transcription and EMR processing
* Stores and retrieves Audio Files and EMR records 
### AI-Core
* Built with FastAP<PERSON> (Python)
* Provides AI-based transcription for real-time voice-to-text
* Uses a separate AI model for EMR generation
* Exposes a REST API for the backend service to access AI-driven data
* Designed for scalability and integration with external AI providers
