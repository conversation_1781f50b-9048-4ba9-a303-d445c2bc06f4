"""
Shared configuration package for cloudseeder FastAPI applications and packages.
"""
from typing import Dict, Any, Callable, Type
from pydantic_settings import BaseSettings
from dotenv import load_dotenv
import os
import json
import logging
from pydantic import ConfigDict

logger = logging.getLogger(__name__)

# Internal cache for singleton behavior
_settings_cache = {}

# Contract: Each app supplies its app_name and its Pydantic config model (Config)
# Usage: settings, config_provider = create_app_settings("property_scout", PropertyScoutConfig)

def create_app_settings(
    app_name: str,
    config_class: Type[BaseSettings],
    env_file: str = ".env"
) -> tuple[BaseSettings, Callable[[], Dict[str, Any]]]:
    """
    Factory to create an app's settings and a config_provider for DI, using normalized env_prefix and env_file.
    Singleton: returns the same instance for each unique (app_name, config_class, env_file).
    """
    # cache_key = (app_name, config_class, env_file)
    cache_key = (app_name, config_class)
    if cache_key in _settings_cache:
        return _settings_cache[cache_key]

    load_dotenv(env_file, override=True)

    # Dynamically create a BaseSettings subclass that mixes in the fields from config_class (which can be a BaseModel)
    class AppSettings(BaseSettings, config_class):
        model_config = ConfigDict(env_prefix=f"{app_name.upper()}_", extra="ignore", env_file=env_file)

    settings = AppSettings()
    result = (settings, lambda: settings.model_dump())
    _settings_cache[cache_key] = result

    logger.info(f"Creating Settings: {json.dumps(settings.model_dump(), indent=2, sort_keys=True)}")
    return result

def get_settings() -> BaseSettings:
    """
    Returns the singleton settings instance if set, otherwise raises an error.
    """
    if not _settings_cache:
        raise RuntimeError("Settings cache has not been set. Call create_app_settings() first.")
    # Return the first (and only) settings instance
    return next(iter(_settings_cache.values()))[0]

def destroy_settings():
    """
    Clears the settings cache and resets all state information.
    Use this to fully reset configuration state (e.g., for testing).
    """
    _settings_cache.clear()

# For packages (auth, database, queues, mail):
# They should accept a config_provider: Callable[[], Dict[str, Any]]
# and use it to access config values, remaining decoupled from app specifics.
