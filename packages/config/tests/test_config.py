import os
import tempfile
import pytest
from pydantic import BaseModel
from pydantic_settings import BaseSettings
from config import create_app_settings
from config.config import get_settings, destroy_settings

# Use a plain class for DummyConfig to avoid MRO issues
class DummyConfig:
    foo: str = "bar"
    num: int = 42

def test_create_app_settings_and_get_settings():
    destroy_settings()  # Ensure clean state
    settings, provider = create_app_settings("dummy", DummyConfig)
    assert settings.foo == "bar"
    assert settings.num == 42
    # get_settings should return the same instance
    settings2 = get_settings()
    assert settings2 is settings
    # provider returns a dict
    conf = provider()
    assert conf["foo"] == "bar"
    assert conf["num"] == 42

def test_destroy_settings():
    destroy_settings()
    with pytest.raises(RuntimeError):
        get_settings()

def test_create_app_settings_loads_env():
    destroy_settings()
    class DummyConfig(BaseModel):
        foo: str = "bar"
        port: int = 1234

    with tempfile.NamedTemporaryFile(mode="w", delete=False) as tmp:
        tmp.write("DUMMYAPP_FOO=fromenv\nDUMMYAPP_PORT=5678\n")
        tmp_path = tmp.name

    try:
        settings, provider = create_app_settings("dummyapp", DummyConfig, env_file=tmp_path)
        assert settings.foo == "fromenv"
        assert settings.port == 5678
        assert provider()["foo"] == "fromenv"
        assert provider()["port"] == 5678

        s = get_settings()
        assert s.foo == "fromenv"
        assert s.port == 5678

    finally:
        os.remove(tmp_path)
        destroy_settings()

def test_get_settings_raises_if_not_set(monkeypatch):
    destroy_settings()
    from config import config as config_mod
    config_mod._settings_cache.clear()
    with pytest.raises(RuntimeError):
        get_settings()

def test_singleton_behavior():
    destroy_settings()
    class DummyConfig(BaseModel):
        foo: str = "bar"
    s1, _ = create_app_settings("singletonapp", DummyConfig)
    s2, _ = create_app_settings("singletonapp", DummyConfig)
    settings = get_settings()
    assert settings.foo == "bar"
    assert s1 is s2

def test_different_app_name_or_class_gives_different_singleton():
    destroy_settings()
    class DummyConfigA(BaseModel):
        foo: str = "bar"
    class DummyConfigB(BaseModel):
        bar: str = "baz"
    s1, _ = create_app_settings("app1", DummyConfigA)
    s2, _ = create_app_settings("app2", DummyConfigA)
    s3, _ = create_app_settings("app1", DummyConfigB)
    assert s1 is not s2
    assert s1 is not s3

def test_destroy_settings_clears_cache():
    destroy_settings()
    class DummyConfig(BaseModel):
        foo: str = "bar"
    create_app_settings("destroytest", DummyConfig)
    assert get_settings().foo == "bar"
    destroy_settings()
    with pytest.raises(RuntimeError):
        get_settings()
