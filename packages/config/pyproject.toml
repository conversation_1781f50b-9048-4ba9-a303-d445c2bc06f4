[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "config"
version = "0.1.0"
description = "Shared configuration package for cloudseeder FastAPI applications and packages."
authors = [
    { name = "CloudSeeder Team", email = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "pydantic-settings>=2.0.0",
    "python-dotenv>=1.0.0"
]

[project.urls]
Homepage = "https://github.com/your-org/cloudseeder"

[tool.setuptools.packages.find]
where = ["src"]
