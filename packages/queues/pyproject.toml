[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "queues"
version = "0.1.0"
description = "A queue management library for CloudSeeder2"
authors = [{ name = "GroGBot", email = "<EMAIL>" }]
license = { file = "LICENSE" }
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "boto3",
    "azure-servicebus",
    "azure-storage-queue"
]

[project.optional-dependencies]
dev = ["pytest", "black", "mypy"]

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
queues = ["*.json"]
