import pytest
from unittest.mock import patch, MagicMock
from queues.interface import Queue<PERSON><PERSON>
from queues.message import Message
from queues.local import LocalQueue
from queues.noop import NoOpQueue
from queues.factory import get_queue_client
import uuid

def test_queueclient_is_abstract():
    with pytest.raises(TypeError):
        QueueClient()

def test_message_model_defaults():
    msg = Message(name="svc", src="src", method="m", type="async")
    assert isinstance(msg.id, uuid.UUID)
    assert msg.ts is not None
    assert msg.type == "async"
    assert msg.data is None

def test_local_queue_delete_message_and_empty():
    q = LocalQueue()
    q.send_message("foo")
    assert q.receive_message() == "foo"
    # Deleting a message should not raise
    q.delete_message("bar")
    # Queue is empty
    assert q.receive_message() is None

def test_noop_queue_methods():
    q = NoOpQueue()
    q.send_message("foo")
    assert q.receive_message() is None
    q.delete_message("bar")
    assert q.get_message_count() == 0

def test_factory_invalid_type():
    with pytest.raises(ValueError):
        get_queue_client("bad", queue_type="notarealtype")

def test_sqs_queue_error_handling():
    with patch("queues.sqs.boto3.Session") as mock_session:
        mock_client = MagicMock()
        mock_session.return_value.client.return_value = mock_client
        from queues.sqs import SQSQueue
        queue = SQSQueue("https://sqs.us-east-1.amazonaws.com/123/queue", "key", "secret")
        # Simulate AWS client raising an error
        mock_client.send_message.side_effect = Exception("fail")
        with pytest.raises(Exception):
            queue.send_message("msg")
        mock_client.receive_message.side_effect = Exception("fail")
        with pytest.raises(Exception):
            queue.receive_message()
        mock_client.delete_message.side_effect = Exception("fail")
        with pytest.raises(Exception):
            queue.delete_message("id")
        mock_client.get_queue_attributes.side_effect = Exception("fail")
        with pytest.raises(Exception):
            queue.get_message_count()

def test_factory_config_provider_fn():
    # Should use config_provider_fn for config
    def config_fn():
        return {"queue_type": "local"}
    q = get_queue_client("from_fn", config_provider_fn=config_fn)
    assert isinstance(q, LocalQueue)

def test_factory_singleton_behavior():
    q1 = get_queue_client("singleton", queue_type="local")
    q2 = get_queue_client("singleton", queue_type="local")
    assert q1 is q2
