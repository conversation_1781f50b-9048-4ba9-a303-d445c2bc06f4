import pytest
from unittest.mock import patch, MagicMock
from queues import __version__
from queues.local import LocalQueue
from queues.factory import get_queue_client
from queues.noop import NoOpQueue

# Test version
def test_version():
    assert __version__ == "0.1.0"

# Test LocalQueue basic send/receive
def test_local_queue_send_receive():
    queue = LocalQueue()
    queue.send_message("test")
    assert queue.receive_message() == "test"
    assert queue.receive_message() is None

# Test LocalQueue message count
def test_local_queue_count():
    queue = LocalQueue()
    assert queue.get_message_count() == 0
    queue.send_message("a")
    queue.send_message("b")
    assert queue.get_message_count() == 2
    queue.receive_message()
    assert queue.get_message_count() == 1

# Test NoOpQueue
def test_noop_queue():
    queue = NoOpQueue()
    queue.send_message("msg")  # Should do nothing
    assert queue.receive_message() is None
    assert queue.get_message_count() == 0
    queue.delete_message("id")  # Should do nothing

# Test SQSQueue (mocked)
def test_sqs_queue_send_receive():
    with patch("queues.sqs.boto3.Session") as mock_session:
        mock_client = MagicMock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.receive_message.return_value = {"Messages": ["msg1"]}
        from queues.sqs import SQSQueue
        queue = SQSQueue("https://sqs.us-east-1.amazonaws.com/123/queue", "key", "secret")
        queue.send_message("hello")
        mock_client.send_message.assert_called_once()
        assert queue.receive_message() == "msg1"
        queue.delete_message("receipt")
        mock_client.delete_message.assert_called_once()
        mock_client.get_queue_attributes.return_value = {"Attributes": {"ApproximateNumberOfMessages": "5"}}
        assert queue.get_message_count() == "5"

# Test AzureQueueClient (mocked)
def test_azure_queue_client():
    from unittest.mock import patch, MagicMock
    with patch("queues.azure.AzureQueue.from_connection_string") as mock_from_conn:
        mock_sdk_client = MagicMock()
        mock_from_conn.return_value = mock_sdk_client
        from queues.factory import get_queue_client
        queue = get_queue_client(
            "az",
            queue_type="azure",
            config_provider_fn=lambda: {
                "connection_string": "DefaultEndpointsProtocol=https;AccountName=dummy;AccountKey=dGVzdGtleQ==;EndpointSuffix=core.windows.net",
                "queue_name": "q"
            }
        )
        with patch.object(queue, "send_message") as mock_send, \
             patch.object(queue, "receive_message", return_value="azmsg") as mock_recv, \
             patch.object(queue, "delete_message") as mock_del, \
             patch.object(queue, "get_message_count", return_value=7) as mock_count:
            queue.send_message("msg")
            mock_send.assert_called_once_with("msg")
            assert queue.receive_message() == "azmsg"
            queue.delete_message("id")
            mock_del.assert_called_once_with("id")
            assert queue.get_message_count() == 7

# Test get_queue_client for local and noop
def test_get_queue_client_local_and_noop():
    q1 = get_queue_client("local1", queue_type="local")
    q2 = get_queue_client("noop1", queue_type="noop")
    assert isinstance(q1, LocalQueue)
    assert isinstance(q2, NoOpQueue)
    # Singleton behavior
    assert get_queue_client("local1") is q1
    assert get_queue_client("noop1") is q2
