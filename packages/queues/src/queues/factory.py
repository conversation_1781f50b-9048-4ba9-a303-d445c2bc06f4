from .local import LocalQueue
from .sqs import SQSQueue
from .azure import AzureQueueClient
from .noop import NoOpQueue

# Singleton dictionary to store named queue instances
_queues = {}

def get_queue_client(name: str, queue_type=None, config_provider_fn=None, **kwargs):
    """
    Retrieves a queue client instance. If a queue with the same name already exists, returns the existing instance.
    Uses config_provider if provided, otherwise falls back to kwargs.
    :param name: The unique name of the queue instance.
    :param queue_type: Type of queue to create (local, sqs, azure, noop). If None, will use config.
    :param config_provider_fn: Optional config provider callable.
    :param kwargs: Additional arguments for queue initialization.
    :return: A singleton instance of the requested queue.
    """
    if name in _queues:
        return _queues[name]

    config = config_provider_fn() if config_provider_fn else kwargs
    qtype = queue_type or config.get("queue_type", "local")

    if qtype == "noop":
        queue_instance = NoOpQueue()
    elif qtype == "local":
        queue_instance = LocalQueue()
    elif qtype == "sqs":
        queue_instance = SQSQueue(name, config["aws_access_key_id"], config["aws_secret_access_key"])
    elif qtype == "azure":
        queue_instance = AzureQueueClient(config["connection_string"], config["queue_name"])
    else:
        raise ValueError(f"Invalid queue type: {qtype}")

    _queues[name] = queue_instance
    return queue_instance
