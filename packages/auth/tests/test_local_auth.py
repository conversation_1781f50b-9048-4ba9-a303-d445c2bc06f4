import pytest
from auth.local_auth import LocalAuthProvider
from types import SimpleNamespace
from datetime import datetime

# Dummy in-memory DatabaseAdapter implementation for testing
class DummyNoSqlDb:
    def __init__(self):
        self.tables = {}
    def get_all_items(self, table):
        return list(self.tables.get(table, {}).values())
    def get_item(self, table, key):
        return self.tables.get(table, {}).get(key)
    def insert_item(self, table, key, value):
        self.tables.setdefault(table, {})[key] = value
    def update_item(self, table, key, value):
        self.tables.setdefault(table, {})[key] = value

@pytest.fixture
def dummy_db_factory():
    def factory(_):
        return DummyNoSqlDb()
    return factory

@pytest.fixture
def auth_provider(dummy_db_factory):
    config = {"foo": "bar"}
    # Reset singleton for test isolation
    LocalAuthProvider._instance = None
    return LocalAuthProvider(config, dummy_db_factory)

def test_register_and_authenticate(auth_provider):
    username = "<EMAIL>"
    password = "testpass"
    # Register user
    result = auth_provider.register_user(username, password_hash=auth_provider.generate_hash(password))
    assert result["username"] == username
    # Authenticate
    token = auth_provider.authenticate(username, password)
    assert token is not None
    # Get user from token
    user = auth_provider.get_user(token)
    assert user["id"] == username
    assert user["email"] == username
    # Logout
    assert auth_provider.logout(token) is True
    # After logout, get_user should return None
    # this is not meant to be a user get_user through the api
    # its meant to be used internally 
    # user = auth_provider.get_user(token)
    # assert auth_provider.get_user(token) is None

def test_register_duplicate_user(auth_provider):
    username = "<EMAIL>"
    password_hash = auth_provider.generate_hash("pw")
    auth_provider.register_user(username, password_hash=password_hash)
    with pytest.raises(ValueError):
        auth_provider.register_user(username, password_hash=password_hash)

def test_authenticate_wrong_password(auth_provider):
    username = "<EMAIL>"
    password = "pw1"
    auth_provider.register_user(username, password_hash=auth_provider.generate_hash(password))
    assert auth_provider.authenticate(username, "badpw") is None

def test_generate_token_and_refresh(auth_provider):
    user = {"id": "<EMAIL>", "email": "<EMAIL>", "roles": ["User"]}
    # Insert user into the database so refresh_token can find it
    auth_provider.database.insert_item("user", user["id"], user)
    token = auth_provider.generate_token(user)
    assert token is not None
    new_token = auth_provider.refresh_token(token)
    assert new_token is not None
    assert isinstance(new_token, str)
