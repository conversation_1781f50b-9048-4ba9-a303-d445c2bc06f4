import pytest
from unittest.mock import patch, MagicMock
from auth.cognito import CognitoAuthProvider

def test_cognito_auth_provider_factory_instantiates():
    config = {"user_pool_id": "pool123", "client_id": "client456"}
    with patch("boto3.client") as mock_boto:
        mock_boto.return_value = MagicMock()
        provider = CognitoAuthProvider(config)
        assert isinstance(provider, CognitoAuthProvider)
        assert provider.user_pool_id == "pool123"
        assert provider.client_id == "client456"
        # The boto3 client should be created for cognito-idp
        mock_boto.assert_called_once_with("cognito-idp")
