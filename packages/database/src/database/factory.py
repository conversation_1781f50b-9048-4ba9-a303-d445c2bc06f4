from typing import Dict
from .interface import <PERSON><PERSON>dapter
from typing import Callable, Dict
from database.interface import <PERSON>Adapter
from database.tinydb import TinyDBDatabase
from database.dynamodb_database import DynamoDBDatabase
from database.filesystem_database import FilesystemDatabase
from database.s3_database import S3Database
from database.sqlite_database import SqliteDatabase

# FIXME LIST ALL CONFIG FIELDS WHICH AFFECT THE DATABASE PACKAGE
# DATABASE_TYPES = ["dynamodb", "tinydb", "s3", "filesystem", "sqlite"]
# DATABASE_DIR

def get_database(config_provider: Callable[[], Dict[str, str]]) -> DatabaseAdapter:
    config = config_provider()
    database_type = config.get("database_type", "").lower()
    if database_type == "dynamodb":
        return DynamoDBDatabase(config=config)
    elif database_type == "tinydb":
        return TinyDBDatabase(config=config)
    elif database_type == "s3":
        return S3Database(config=config)
    elif database_type == "filesystem":
        return FilesystemDatabase(config=config)
    elif database_type == "sqlite":
        return SqliteDatabase(config=config)
    elif database_type == "none":
        return None
    else:
        raise ValueError(f"Unsupported database type: {database_type}")


def get_db(config_provider: Callable[[], Dict[str, str]]) -> DatabaseAdapter:
    """
    Load database implementation dynamically based on a configuration provider.

    :param config_provider: A callable that returns the database configuration.
    :return: An instance of DatabaseAdapter.
    """
    return get_database(config_provider=config_provider)