import sqlite3
import os
import logging
import re
from typing import Dict, List, Any
from .interface import DatabaseAdapter
from .record_transformer import RecordTransformer
import threading

logger = logging.getLogger(__name__)

class SqliteDatabase(DatabaseAdapter):
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, config: Dict[str, str]):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super(SqliteDatabase, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self, config: Dict[str, str]):
        if getattr(self, '_initialized', False):
            return
        self._initialized = True
        self.config = config
        self.sqlite_path = config.get("sqlite_path", os.path.join("data", "sqlite_db.sqlite3"))
        dirpath = os.path.dirname(self.sqlite_path)
        if dirpath:
            os.makedirs(dirpath, exist_ok=True)
        # Allow usage across threads
        self.conn = sqlite3.connect(self.sqlite_path, check_same_thread=False)
        self.conn.row_factory = sqlite3.Row
        self.transformer = RecordTransformer()
        logger.info(f"SqliteDatabase initialized at {self.sqlite_path}")

    def _get_connection(self):
        """Get a new SQLite connection for each operation (thread-safe)."""
        conn = sqlite3.connect(self.sqlite_path, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        return conn

    def _ensure_table(self, table: str):
        conn = self._get_connection()
        cur = conn.cursor()
        cur.execute(f"""
            CREATE TABLE IF NOT EXISTS {table} (
                id TEXT PRIMARY KEY,
                json TEXT
            )
        """)
        conn.commit()
        conn.close()

    def _ensure_table_with_columns(self, table: str, columns: List[str]):
        conn = self._get_connection()
        cur = conn.cursor()
        
        # Check if table exists
        cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
        table_exists = cur.fetchone() is not None
        
        if not table_exists:
            # Create table with specified columns
            id_col = "id TEXT PRIMARY KEY"
            other_cols = [f"{col} TEXT" for col in columns if col != 'id']
            all_cols = [id_col] + other_cols
            cols_def = ", ".join(all_cols)
            cur.execute(f"CREATE TABLE {table} ({cols_def})")
        else:
            # Add missing columns to existing table
            cur.execute(f"PRAGMA table_info({table})")
            existing_columns = {row[1] for row in cur.fetchall()}
            for col in columns:
                if col not in existing_columns:
                    cur.execute(f"ALTER TABLE {table} ADD COLUMN {col} TEXT")
        
        conn.commit()
        conn.close()

    def upsert_item(self, table: str, key: str, item: dict) -> dict:
        flat_item = self.transformer.flatten(item)
        columns = list(flat_item.keys())
        values = [flat_item[col] for col in columns]
        if 'id' not in columns:
            columns = ['id'] + columns
            values = [key] + values
        self._ensure_table_with_columns(table, columns)
        conn = self._get_connection()
        cur = conn.cursor()
        cols = ', '.join(columns)
        placeholders = ', '.join(['?'] * len(values))
        sql = f"INSERT OR REPLACE INTO {table} ({cols}) VALUES ({placeholders})"
        cur.execute(sql, values)
        conn.commit()
        conn.close()
        return item

    def insert_item(self, table: str, key: str, item: dict) -> dict:
        return self.upsert_item(table, key, item)

    def get_item(self, table: str, key: str) -> dict:
        self._ensure_table(table)
        conn = self._get_connection()
        cur = conn.cursor()
        cur.execute(f"PRAGMA table_info({table})")
        columns = [row[1] for row in cur.fetchall()]
        cur.execute(f"SELECT * FROM {table} WHERE id = ?", (key,))
        row = cur.fetchone()
        conn.close()
        if row:
            row_dict = dict(zip(columns, row))
            return self.transformer.unflatten(row_dict)
        return {}

    def get_binary_item(self, table: str, key: str) -> bytes:
        self._ensure_table(table)
        conn = self._get_connection()
        cur = conn.cursor()
        cur.execute(f"SELECT json FROM {table} WHERE id = ?", (key,))
        row = cur.fetchone()
        conn.close()
        if row:
            # Return the JSON column as bytes
            return row[0].encode("utf-8")
        raise ValueError(f"Item with key {key} not found in {table}")

    def get_all_items(self, table: str) -> list:
        try:
            self._ensure_table(table)
            conn = self._get_connection()
            cur = conn.cursor()
            cur.execute(f"PRAGMA table_info({table})")
            columns = [row[1] for row in cur.fetchall()]
            cur.execute(f"SELECT * FROM {table}")
            rows = cur.fetchall()
            conn.close()
            return [self.transformer.unflatten(dict(zip(columns, row))) for row in rows]
        except Exception as e:
            logger.error(f"Error retrieving all items from {table}: {e}")
            return []

    def update_item(self, table: str, key: str, updates: dict) -> dict:
        item = self.get_item(table, key)
        if not item:
            return {}
        item.update(updates)
        return self.insert_item(table, key, item)

    def delete_item(self, table: str, key: str) -> None:
        self._ensure_table(table)
        conn = self._get_connection()
        cur = conn.cursor()
        cur.execute(f"DELETE FROM {table} WHERE id = ?", (key,))
        conn.commit()
        conn.close()

    def query_items(self, table_name: str, criteria: dict) -> list:
        self._ensure_table(table_name)
        conn = self._get_connection()
        cur = conn.cursor()
        cur.execute(f"PRAGMA table_info({table_name})")
        columns = [row[1] for row in cur.fetchall()]
        cur.execute(f"SELECT * FROM {table_name}")
        rows = cur.fetchall()
        conn.close()
        results = []
        for row in rows:
            row_dict = dict(zip(columns, row))
            item = self.transformer.unflatten(row_dict)
            if all(item.get(k) == v for k, v in criteria.items()):
                results.append(item)
        return results

    def search_by_key_part(self, table: str, key_part: str, regex: bool = False) -> List[Dict[str, Any]]:
        self._ensure_table(table)
        conn = self._get_connection()
        cur = conn.cursor()
        cur.execute(f"PRAGMA table_info({table})")
        columns = [row[1] for row in cur.fetchall()]
        cur.execute(f"SELECT * FROM {table}")
        rows = cur.fetchall()
        conn.close()
        results = []
        for row in rows:
            row_dict = dict(zip(columns, row))
            item = self.transformer.unflatten(row_dict)
            id_val = row_dict.get('id', '')
            if regex:
                if re.search(key_part, id_val):
                    results.append(item)
            else:
                if id_val.startswith(key_part):
                    results.append(item)
        return results

    def copy_table(self, source_table: str, dest_table: str) -> None:
        self._ensure_table(source_table)
        self._ensure_table(dest_table)
        conn = self._get_connection()
        cur = conn.cursor()
        cur.execute(f"PRAGMA table_info({source_table})")
        columns = [row[1] for row in cur.fetchall()]
        cur.execute(f"SELECT * FROM {source_table}")
        rows = cur.fetchall()
        conn.close()
        for row in rows:
            row_dict = dict(zip(columns, row))
            item = self.transformer.unflatten(row_dict)
            self.insert_item(dest_table, row_dict['id'], item)

    def query(self, querystr: str) -> list:
        conn = self._get_connection()
        cur = conn.cursor()
        cur.execute(querystr)
        if cur.description:  # SELECT or similar
            columns = [desc[0] for desc in cur.description]
            rows = cur.fetchall()
            conn.close()
            # Always unflatten rows before returning
            return [self.transformer.unflatten(dict(zip(columns, row))) for row in rows]
        else:  # INSERT, UPDATE, DELETE, etc.
            conn.commit()
            conn.close()
            return []

    def insert_columns(
        self,
        table: str,
        columns: List[str],
        values: List[Any],
        conflict_strategy: str = "REPLACE"
    ) -> None:
        if len(columns) != len(values):
            raise ValueError(f"Number of columns ({len(columns)}) must match number of values ({len(values)})")
        self._ensure_table_with_columns(table, columns)
        conn = self._get_connection()
        cur = conn.cursor()
        cols = ', '.join(columns)
        placeholders = ', '.join(['?'] * len(values))
        sql = f"INSERT OR {conflict_strategy} INTO {table} ({cols}) VALUES ({placeholders})"
        try:
            cur.execute(sql, values)
            conn.commit()
            conn.close()
            logger.debug(f"Inserted row into {table} with columns {columns}")
        except Exception as e:
            logger.error(f"Failed to insert into {table}: {e}")
            conn.close()
            raise
