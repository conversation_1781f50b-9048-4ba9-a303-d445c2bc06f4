# Complete User Creation Flow

## 🔍 Authentication Check

First, check if authentication is enabled in your environment:

```bash
# Check your .env file
grep AUTH_ENABLED apps/ai_core/.env
```

## 🚀 Scenario 1: Authentication Enabled (Production/Secure)

### Step 1: Login with <PERSON> Admin
```http
POST http://localhost:8001/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "wD7_wTDQ2gIFc2IgNj8J1w"
}
```

**Expected Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

### Step 2: Create User with Token
```http
POST http://localhost:8001/v1/user
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "id": "<EMAIL>",
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password_hash": "c3868f06cedaf163c9ef0ca5d88b69bdf5a1a47acf3a8b0d58e966007464bec4",
  "roles": ["User"]
}
```

## 🔓 Scenario 2: Authentication Disabled (Development)

If `AI_CORE_AUTH_ENABLED=False`, you can create users directly without a token:

```http
POST http://localhost:8001/v1/user
Content-Type: application/json

{
  "id": "<EMAIL>",
  "name": "John Doe", 
  "email": "<EMAIL>",
  "password_hash": "c3868f06cedaf163c9ef0ca5d88b69bdf5a1a47acf3a8b0d58e966007464bec4",
  "roles": ["User"]
}
```

## 🔑 Password Hashing

⚠️ **Important**: The API expects `password_hash`, not plain text passwords.

### Option 1: Use Online SHA-256 Tool
1. Go to https://emn178.github.io/online-tools/sha256.html
2. Enter your password (e.g., "mypassword123")
3. Copy the hash (e.g., "c3868f06cedaf163c9ef0ca5d88b69bdf5a1a47acf3a8b0d58e966007464bec4")

### Option 2: Use Python
```python
import hashlib

def hash_password(password):
    return hashlib.sha256(password.encode()).hexdigest()

# Example
password = "mypassword123"
password_hash = hash_password(password)
print(password_hash)
# Output: c3868f06cedaf163c9ef0ca5d88b69bdf5a1a47acf3a8b0d58e966007464bec4
```

### Option 3: Use Command Line
```bash
echo -n "mypassword123" | shasum -a 256
```

## 📋 Complete Postman Workflow

### Collection Variables
```json
{
  "base_url": "http://localhost:8001",
  "admin_email": "<EMAIL>",
  "admin_password": "wD7_wTDQ2gIFc2IgNj8J1w",
  "auth_token": ""
}
```

### Request 1: Login (if auth enabled)
```http
POST {{base_url}}/v1/auth/login
Content-Type: application/json

{
  "email": "{{admin_email}}",
  "password": "{{admin_password}}"
}
```

**Test Script** (to save token):
```javascript
if (pm.response.code === 200) {
    const response = pm.response.json();
    pm.collectionVariables.set("auth_token", response.access_token);
}
```

### Request 2: Create User
```http
POST {{base_url}}/v1/user
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "id": "<EMAIL>",
  "name": "John Doe",
  "email": "<EMAIL>",
  "password_hash": "c3868f06cedaf163c9ef0ca5d88b69bdf5a1a47acf3a8b0d58e966007464bec4",
  "roles": ["User"]
}
```

## 🔄 Alternative: Registration Flow

If you want users to self-register:

```http
POST {{base_url}}/v1/registration
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "plaintext_password_here",
  "state": "registered"
}
```

## 🛠 Troubleshooting

### Error: 401 Unauthorized
- Check if authentication is enabled
- Verify your admin credentials
- Ensure token is valid and not expired

### Error: 403 Forbidden
- User doesn't have Admin role
- Use the master admin account

### Error: 400 Bad Request
- Check password_hash format
- Verify all required fields are present
- Ensure email format is valid

### Error: 500 Internal Server Error
- Check server logs
- Verify database connectivity
- Check if user already exists

## 🎯 Quick Test Commands

### Check if server is running:
```bash
curl http://localhost:8001/v1/version
```

### Test login:
```bash
curl -X POST http://localhost:8001/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"wD7_wTDQ2gIFc2IgNj8J1w"}'
```

### Test user creation (no auth):
```bash
curl -X POST http://localhost:8001/v1/user \
  -H "Content-Type: application/json" \
  -d '{"id":"<EMAIL>","name":"Test User","email":"<EMAIL>","password_hash":"c3868f06cedaf163c9ef0ca5d88b69bdf5a1a47acf3a8b0d58e966007464bec4","roles":["User"]}'
```
