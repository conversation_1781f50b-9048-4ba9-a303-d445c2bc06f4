# Scribble Assessment Sequence Diagram

```mermaid
sequenceDiagram
  participant Clinician
  participant <PERSON><PERSON><PERSON><PERSON> as Client App
  participant Backend
  participant <PERSON><PERSON><PERSON><PERSON> as MongoDB
  participant <PERSON><PERSON><PERSON> as AI Core
  participant S3Bucket as S3 Bucket

  Clinician->>ClientApp: Login
  ClientApp->>Backend: Request Scheduling Data
  Backend->>MongoDB: Query Scheduling and Patient Data
  MongoDB-->>Backend: Return Scheduling and Patient Data
  Backend->>ClientApp: Display Scheduling and Patient Data
  Clinician->>ClientApp: Request Start of Care Assessment
  
  Clinician->>ClientApp: Start Assessment
  Clinician->>ClientApp: Submit Assessment
  ClientApp->>Backend: Post Audio File
  
  Backend->>S3Bucket: Upload Audio File and Questions
  Backend->>AICore: Send Message to AI Core for Processing
  S3Bucket-->>AICore: Get Audio File And Questions
  AICore->>AICore: Transcribe Audio File
  AICore->>AICore: Answer Questions
  AICore->>Backend: Send Message of Processing Results
  Backend->>Backend: Merge Answers with Start of Care Template
  Backend->>MongoDB: Store Merged Answers with Start of Care Form Template
  Backend->>ClientApp: Notify Client of Processing Results
  Clinician->>ClientApp: Click to Review Merged Answers
  Backend->>ClientApp: Display Merged Answers with Start of Care Form Template
  Clinician->>ClientApp: Edit or Accept Merged Answers
  ClientApp->>Backend: Update Merged Answers
  Clinician->>ClientApp: Submit Merged Answers
  Backend->>Backend: Transform Merge Answers into KanTime RPA JSON
  Backend->>RPA: Submit Data to KanTime RPA
```


# Ai Core - FastAPI Application

This is a FastAPI application for `ai_core`.

## 🚀 Installation & Setup

### Create a Virtual Environment
```sh
cd apps/ai_core
python -m venv venv
source venv/bin/activate  # On Windows, use venv\Scripts\activate
```

### Install Dependencies
```sh
cd apps/ai_core
pip install -e .

# move to the packages directory and install the desired packages
cd ../../packages/auth
pip install -e .
cd ../database
pip install -e .
cd ../queues
pip install -e .
cd ../config
pip install -e .
```

### Run the Application on Bare Metal
```sh
cd apps/ai_core
python -m uvicorn ai_core.main:app --reload
```

Ai Core Should be available at http://localhost:8000

### Run the Application in Docker Locally
```sh
# run this from above the apps directory
docker build -t ai_core -f apps/ai_core/Dockerfile .
docker run --env-file .env --rm --name ai_core -p 8000:8000 ai_core

# use this one to test locally
docker run --env-file .env --rm --name ai_core -p 8001:8001 ai_core

# interactively
cd apps/ai_core/
docker run --env-file .env --rm -it ai_core /bin/sh

# AWS ECR

# Login to AWS ECR
# aws configure
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 891377302853.dkr.ecr.us-west-2.amazonaws.com

# Build and push the image
cd /mnt/2tb/github/cloudseeder

# TODO - make build into different github action - in Scribble repo
docker build -t ai_core:latest -f apps/ai_core/Dockerfile .

docker tag ai_core:latest 891377302853.dkr.ecr.us-west-2.amazonaws.com/ai_core:latest
docker push 891377302853.dkr.ecr.us-west-2.amazonaws.com/ai_core:latest


# Scribble repo fix

# manual update of lambda reference

# deploy in console 
# FIXME - change to aws cli
# FIXME - very good logging ! exception handling ! details use request id
    
```


## 🧪 Testing

### Run Tests
```sh
pytest
```

### Run Tests with Coverage
```sh
pytest --cov=ai_core --cov-report html
```


# EC2 Instance

EC2 instacles 22.04 - t3.medium - make pem file 

```bash
sudo apt update
sudo apt install -y ca-certificates curl gnupg
sudo install -m 0755 -d /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo tee /etc/apt/keyrings/docker.asc > /dev/null
sudo chmod a+r /etc/apt/keyrings/docker.asc
echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
sudo groupadd docker
sudo usermod -aG docker $USER
newgrp docker

# docker ecr login
aws configure
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 891377302853.dkr.ecr.us-west-2.amazonaws.com

# run ai_core
docker pull 891377302853.dkr.ecr.us-west-2.amazonaws.com/ai_core:latest

# with env file
docker run --env-file .env --rm --name ai_core -p 8001:8001 891377302853.dkr.ecr.us-west-2.amazonaws.com/ai_core:latest
# with env var
docker run -e VAR_NAME=value --rm --name ai_core -p 8001:8001 891377302853.dkr.ecr.us-west-2.amazonaws.com/ai_core:latest


# Docker Prod Deployment

aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 891377302853.dkr.ecr.us-west-2.amazonaws.com
docker pull 891377302853.dkr.ecr.us-west-2.amazonaws.com/ai_core:latest
docker stop ai_core 2>/dev/null || true
docker rm ai_core 2>/dev/null || true
docker run --pull=always --env-file ${HOME}/ai_core/.env --rm -d --name ai_core -p 8001:8001 891377302853.dkr.ecr.us-west-2.amazonaws.com/ai_core:latest


```

Make a script to download input from prod upload it to dev (dev1) and fire off transcription.
* env vars for both aws
* download from one 
* upload
* send processing request

```json
{
  "id": "123e4567-e89b-12d3-a456-426614174999",
  "user_id": "<EMAIL>",
  "visit_id": "6852dc20c65e9aad69a59b30",
  "client_id": "client_id-*********",
  "assessment_id": "6852dc20c65e9aad69a59b34",
  "company_id": "dev1",
  "transcribe_type": "deepgram",
  "audio_files": [
    "recording-1750730394734.m4a"
  ],
  "question_files": [
    "questionForm.json"
  ],
  "mode": "all-stages",
  "status": "error",
  "started": 1683*********,
  "completed": 1683*********,
  "exception": "An error occurred",
  "answer_files": [],
  "transcription_files": [],
  "conversation_files": [],
  "total_questions": 5,
  "answered_questions_cnt": 3,
  "answered_questions": [],
  "total_time": 120,
  "transcription_time": 60,
  "transcription_service_type": "deepgram",
  "transcription_service_model": "whisper",
  "ai_service_type": "openai",
  "ai_service_model": "gpt-4",
  "ai_temperature": 0,
  "ai_prompt": "Please answer the following questions.",
  "ai_embeddings": [
  ],
  "audio_buffer_size": 0,
  "version": "0.1.0",
  "commit": "*********0"
}
```
