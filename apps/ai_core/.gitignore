# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Virtual environment
venv/
new-venv/
env/
.venv/
.conda/

# Distribution / packaging
build/
dist/
*.egg-info/
*.egg
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
.coverage
.coverage.*
htmlcov/
nosetests.xml
pytest_cache/
.coverage
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints/

# PyCharm
.idea/
*.iml

# VS Code
.vscode/

# Spyder
.spyderproject
.spyder2

# MyPy
.mypy_cache/

# Pytest
.pytest_cache/

# Bandit
.bandit/

# Cache files
.cache/
*.log
*.bak

# Cython / compiled files
*.c
*.so

# MacOS files
.DS_Store

# Windows files
Thumbs.db
Desktop.ini

# dotenv environment variables
.env
.env.local
.env.dev
.env.prod
.env.*.local


# AWS credential files
.aws/

# Terraform
.terraform/
*.tfstate
*.tfstate.backup

# Ansible
*.retry

# SQLite database files
*.db
*.sqlite3

# Logs and debugging files
logs/
*.log
debug.log
error.log

# Lock files
poetry.lock
Pipfile.lock


# tinydb
db.json

# apps
cert.pem
key.pem

# tinydb
db.json

# apps
data
work
# input
# output
# data/filesystem_db

!tests/data
version.json
scrapy/ai_realestate_scraper/listings/
nginx-letsencrypt/*
nginx-data/*
