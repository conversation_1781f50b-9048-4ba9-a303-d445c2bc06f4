action-msgs==1.2.1
action-tutorials-interfaces==0.20.5
action-tutorials-py==0.20.5
actionlib-msgs==4.2.4
aenum==3.1.15
aiofiles==24.1.0
aiohappyeyeballs==2.4.4
aiohttp==3.11.10
aiosignal==1.3.2
ament-cmake-test==1.3.11
ament-copyright==0.12.11
ament-cppcheck==0.12.11
ament-cpplint==0.12.11
ament-flake8==0.12.11
ament-index-python==1.4.0
ament-lint==0.12.11
ament-lint-cmake==0.12.11
ament-package==0.14.0
ament-pep257==0.12.11
ament-uncrustify==0.12.11
ament-xmllint==0.12.11
angles==1.15.0
annotated-types==0.7.0
anyio==4.7.0
appdirs==1.4.4
apturl==0.5.2
argcomplete==3.5.3
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asgiref==3.8.1
asttokens==3.0.0
async-lru==2.0.4
async-timeout==5.0.1
attrs==24.2.0
auth0-python==4.8.1
babel==2.16.0
backoff==2.2.1
bcrypt==4.3.0
beautifulsoup4==4.12.3
beniget==0.4.1
black==24.10.0
bleach==6.2.0
blinker==1.9.0
Brlapi==0.8.3
Brotli==1.0.9
build==1.2.2.post1
builtin-interfaces==1.2.1
cachetools==5.5.2
catkin-pkg-modules==1.0.0
certifi==2020.6.20
cffi==1.17.1
chardet==4.0.0
charset-normalizer==3.4.0
chroma-hnswlib==0.7.6
chromadb==0.6.3
click==8.1.8
colcon-argcomplete==0.3.3
colcon-bash==0.5.0
colcon-cd==0.2.1
colcon-cmake==0.2.29
colcon-common-extensions==0.3.0
colcon-core==0.18.4
colcon-defaults==0.2.9
colcon-devtools==0.3.0
colcon-installed-package-information==0.2.1
colcon-library-path==0.2.1
colcon-metadata==0.2.5
colcon-notification==0.3.0
colcon-output==0.2.13
colcon-override-check==0.0.1
colcon-package-information==0.4.0
colcon-package-selection==0.2.10
colcon-parallel-executor==0.3.0
colcon-pkg-config==0.1.0
colcon-powershell==0.4.0
colcon-python-setup-py==0.2.9
colcon-recursive-crawl==0.2.3
colcon-ros==0.5.0
colcon-test-result==0.3.8
colcon-zsh==0.5.0
colorama==0.4.4
coloredlogs==15.0.1
comm==0.2.2
command-not-found==0.3
composition-interfaces==1.2.1
control-msgs==4.7.0
cov-core==1.15.0
coverage==6.2
crewai==0.102.0
cryptography==44.0.1
cupshelpers==1.0
cv-bridge==3.2.1
cycler==0.11.0
dataclasses-json==0.6.7
datamodel-code-generator==0.25.6
dbus-python==1.2.18
debugpy==1.8.11
decorator==5.1.1
deepgram-sdk==3.7.7
defer==1.0.6
defusedxml==0.7.1
demo-nodes-py==0.20.5
Deprecated==1.2.18
deprecation==2.1.0
diagnostic-msgs==4.2.4
distlib==0.3.9
distro==1.7.0
distro-info==1.1+ubuntu0.2
dnspython==2.1.0
docstring_parser==0.16
docutils==0.17.1
domain-coordinator==0.10.0
duplicity==0.8.21
durationpy==0.9
email_validator==2.2.0
empy==3.3.4
et_xmlfile==2.0.0
example-interfaces==0.9.3
examples-rclpy-executors==0.15.3
examples-rclpy-minimal-action-client==0.15.3
examples-rclpy-minimal-action-server==0.15.3
examples-rclpy-minimal-client==0.15.3
examples-rclpy-minimal-publisher==0.15.3
examples-rclpy-minimal-service==0.15.3
examples-rclpy-minimal-subscriber==0.15.3
exceptiongroup==1.2.2
executing==2.1.0
fastapi==0.115.10
fastapi-code-generator==0.5.2
fasteners==0.14.1
fastjsonschema==2.21.1
ffmpeg-python==0.2.0
filelock==3.17.0
flake8==4.0.1
flatbuffers==25.2.10
fonttools==4.29.1
fqdn==1.5.1
frozenlist==1.5.0
fs==2.4.12
fsspec==2025.2.0
future==0.18.2
gast==0.5.2
gazebo-msgs==3.9.0
GDAL==3.4.1
generate-parameter-library-py==0.4.0
genson==1.3.0
geometry-msgs==4.2.4
google-auth==2.38.0
googleapis-common-protos==1.68.0
gpg==1.16.0
graph-msgs==0.2.0
grpcio==1.70.0
h11==0.14.0
html5lib==1.1
httpcore==1.0.7
httplib2==0.20.2
httptools==0.6.4
httpx==0.27.2
huggingface-hub==0.29.1
humanfriendly==10.0
idna==3.3
image-geometry==3.2.1
importlib_metadata==8.5.0
importlib_resources==6.5.2
inflect==5.6.2
iniconfig==1.1.1
instructor==1.7.2
interactive-markers==2.3.2
ipykernel==6.29.5
ipython==8.30.0
ipywidgets==8.1.5
isoduration==20.11.0
isort==5.13.2
jedi==0.19.2
jeepney==0.7.1
Jinja2==3.1.4
jiter==0.8.2
joint-state-publisher==2.4.0
joint-state-publisher-gui==2.4.0
json5==0.10.0
json_repair==0.39.1
jsonpickle==4.0.2
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter==1.1.1
jupyter-console==6.6.3
jupyter-events==0.10.0
jupyter-lsp==2.2.5
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyter_server==2.14.2
jupyter_server_terminals==0.5.3
jupyterlab==4.3.3
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
jupyterlab_widgets==3.0.13
keyring==23.5.0
kiwisolver==1.3.2
kubernetes==32.0.1
language-selector==0.1
lark==1.1.1
laser-geometry==2.4.0
launch==1.0.7
launch-param-builder==0.1.1
launch-ros==0.19.8
launch-testing==1.0.7
launch-testing-ros==0.19.8
launch-xml==1.0.7
launch-yaml==1.0.7
launchpadlib==1.10.16
lazr.restfulclient==0.14.4
lazr.uri==1.0.6
libvirt-python==8.0.0
lifecycle-msgs==1.2.1
litellm==1.60.2
lockfile==0.12.2
logging-demo==0.20.5
louis==3.20.0
lxml==4.8.0
lz4==3.1.3+dfsg
macaroonbakery==1.3.1
Mako==1.1.3
map-msgs==2.1.0
Markdown==3.3.6
markdown-it-py==3.0.0
MarkupSafe==2.0.1
marshmallow==3.23.1
matplotlib==3.5.1
matplotlib-inline==0.1.7
mccabe==0.6.1
mdurl==0.1.2
meld==3.20.4
meson==0.61.2
message-filters==4.3.5
mistune==3.0.2
mmh3==5.1.0
monotonic==1.6
more-itertools==8.10.0
moveit-configs-utils==2.5.8
moveit-msgs==2.2.1
mpi4py==3.1.3
mpmath==0.0.0
multidict==6.1.0
mypy-extensions==1.0.0
nav-msgs==4.2.4
nbclient==0.10.1
nbconvert==7.16.4
nbformat==5.10.4
nest-asyncio==1.6.0
netifaces==0.11.0
networkx==3.4.2
nose2==0.9.2
notebook==7.3.1
notebook_shim==0.2.4
notify2==0.3
numpy==2.2.3
oauthlib==3.2.2
object-recognition-msgs==2.0.0
octomap-msgs==2.0.1
olefile==0.46
onnxruntime==1.20.1
openai==1.65.1
opencv-python==4.11.0.86
opencv-python-headless==4.11.0.86
openpyxl==3.1.5
opentelemetry-api==1.30.0
opentelemetry-exporter-otlp-proto-common==1.30.0
opentelemetry-exporter-otlp-proto-grpc==1.30.0
opentelemetry-exporter-otlp-proto-http==1.30.0
opentelemetry-instrumentation==0.51b0
opentelemetry-instrumentation-asgi==0.51b0
opentelemetry-instrumentation-fastapi==0.51b0
opentelemetry-proto==1.30.0
opentelemetry-sdk==1.30.0
opentelemetry-semantic-conventions==0.51b0
opentelemetry-util-http==0.51b0
orjson==3.10.15
osrf-pycommon==2.1.4
overrides==7.7.0
packaging==24.2
pandocfilters==1.5.1
paramiko==2.9.3
parso==0.8.4
pathspec==0.12.1
pcl-msgs==1.0.0
pdfminer.six==20231228
pdfplumber==0.11.5
pendulum-msgs==0.20.5
pexpect==4.8.0
pillow==11.1.0
pipenv==2024.4.1
platformdirs==4.3.6
pluggy==0.13.0
ply==3.11
posthog==3.17.0
prometheus_client==0.21.1
prompt_toolkit==3.0.48
propcache==0.2.1
protobuf==5.29.3
psutil==6.1.0
ptyprocess==0.7.0
pure_eval==0.2.3
py==1.10.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pybind11==2.9.1
pycairo==1.20.1
pycodestyle==2.8.0
pycparser==2.22
pycups==2.0.1
pydantic==2.10.5
pydantic_core==2.27.2
pydocstyle==6.1.1
pydot==1.4.2
pyflakes==2.4.0
Pygments==2.18.0
PyGObject==3.42.1
PyJWT==2.10.1
pymacaroons==0.13.0
PyNaCl==1.5.0
pyparsing==2.4.7
pypdfium2==4.30.1
PyPika==0.48.9
pyproject_hooks==1.2.0
PyQt5==5.15.6
PyQt5-sip==12.9.1
pyRFC3339==1.1
PySnooper==1.1.1
pytest==6.2.5
pytest-cov==3.0.0
python-apt==2.4.0+ubuntu4
python-dateutil==2.9.0.post0
python-debian==0.1.43+ubuntu1.1
python-dotenv==1.0.1
python-json-logger==3.2.0
python-magic==0.4.24
python-qt-binding==1.1.2
pythran==0.10.0
pytz==2022.1
pyvis==0.3.2
pyxdg==0.27
PyYAML==6.0.2
pyzmq==26.2.0
qt-dotgraph==2.2.3
qt-gui==2.2.3
qt-gui-cpp==2.2.3
qt-gui-py-common==2.2.3
quality-of-service-demo-py==0.20.5
rcl-interfaces==1.2.1
rclpy==3.3.15
rcutils==5.1.6
referencing==0.35.1
regex==2024.11.6
reportlab==3.6.8
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==0.9.1
resource-retriever==3.1.2
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==13.9.4
rmw-dds-common==1.6.0
roman==3.3
ros2action==0.18.11
ros2bag==0.15.13
ros2cli==0.18.11
ros2component==0.18.11
ros2doctor==0.18.11
ros2interface==0.18.11
ros2launch==0.19.8
ros2lifecycle==0.18.11
ros2multicast==0.18.11
ros2node==0.18.11
ros2param==0.18.11
ros2pkg==0.18.11
ros2run==0.18.11
ros2service==0.18.11
ros2topic==0.18.11
rosbag2-interfaces==0.15.13
rosbag2-py==0.15.13
rosdep==0.25.1
rosdep-modules==0.25.1
rosdistro-modules==1.0.1
rosgraph-msgs==1.2.1
rosidl-adapter==3.1.6
rosidl-cli==3.1.6
rosidl-cmake==3.1.6
rosidl-generator-c==3.1.6
rosidl-generator-cpp==3.1.6
rosidl-generator-py==0.14.4
rosidl-parser==3.1.6
rosidl-runtime-py==0.9.3
rosidl-typesupport-c==2.0.2
rosidl-typesupport-cpp==2.0.2
rosidl-typesupport-fastrtps-c==2.2.2
rosidl-typesupport-fastrtps-cpp==2.2.2
rosidl-typesupport-introspection-c==3.1.6
rosidl-typesupport-introspection-cpp==3.1.6
rospkg-modules==1.6.0
rpds-py==0.22.3
rpyutils==0.2.1
rqt-action==2.0.1
rqt-bag==1.1.5
rqt-bag-plugins==1.1.5
rqt-console==2.0.3
rqt-graph==1.3.1
rqt-gui==1.1.7
rqt-gui-py==1.1.7
rqt-msg==1.2.0
rqt-plot==1.1.3
rqt-publisher==1.5.0
rqt-py-common==1.1.7
rqt-py-console==1.0.2
rqt-reconfigure==1.1.2
rqt-service-caller==1.0.5
rqt-shell==1.0.2
rqt-srv==1.0.3
rqt-topic==1.5.0
rsa==4.9
s3cmd==2.2.0
scipy==1.8.0
scripts==3.9.0
SecretStorage==3.3.1
Send2Trash==1.8.3
sensor-msgs==4.2.4
sensor-msgs-py==4.2.4
shape-msgs==4.2.4
shellingham==1.5.4
six==1.16.0
slack_sdk==3.34.0
sniffio==1.3.1
snowballstemmer==2.2.0
soupsieve==2.6
srdfdom==2.0.7
sros2==0.10.5
ssh-import-id==5.11
stack-data==0.6.3
starlette==0.46.0
statistics-msgs==1.2.1
std-msgs==4.2.4
std-srvs==4.2.4
stereo-msgs==4.2.4
stringcase==1.2.0
sympy==1.9
systemd-python==234
teleop-twist-keyboard==2.4.0
tenacity==9.0.0
terminado==0.18.1
tf2-geometry-msgs==0.25.12
tf2-kdl==0.25.12
tf2-msgs==0.25.12
tf2-py==0.25.12
tf2-ros-py==0.25.12
tf2-tools==0.25.12
tiktoken==0.9.0
tinycss2==1.4.0
tokenizers==0.21.0
toml==0.10.2
tomli==2.2.1
tomli_w==1.2.0
topic-monitor==0.20.5
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
trajectory-msgs==4.2.4
turtlebot3-msgs==2.2.3
turtlebot3-teleop==2.1.5
turtlesim==1.4.2
typed-ast==1.5.5
typeguard==2.2.2
typer==0.12.5
types-python-dateutil==2.9.0.20241206
typing-inspect==0.9.0
typing_extensions==4.12.2
ubuntu-drivers-common==0.0.0
ubuntu-pro-client==8001
ufoLib2==0.13.1
ufw==0.36.1
unattended-upgrades==0.1
unicodedata2==14.0.0
unique-identifier-msgs==2.2.1
urdfdom-py==1.2.1
uri-template==1.3.0
urllib3==2.3.0
usb-creator==0.3.7
uv==0.6.3
uvicorn==0.34.0
uvloop==0.21.0
vboxapi==1.0
virtualenv==20.29.3
virtualenv-clone==0.3.0
visualization-msgs==4.2.4
wadllib==1.3.6
watchfiles==1.0.4
wcwidth==0.2.13
webcolors==24.11.1
webencodings==0.5.1
websocket-client==1.8.0
websockets==13.1
widgetsnbextension==4.0.13
wrapt==1.17.2
xacro==2.0.8
xdg==5
xkit==0.0.0
yarl==1.18.3
zipp==3.21.0
