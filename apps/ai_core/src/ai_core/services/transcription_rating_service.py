# FIXME - Request probably should not be here
# instead individual query parameters should be defined in the app.yml
# then put into a dictionary parameter to the service methods
from fastapi import Request
import logging
from typing import List
import uuid
from config import get_settings
from queues.interface import QueueClient
from database.interface import DatabaseAdapter
from database.factory import get_database
from ai_core.models.transcription_rating import TranscriptionRating
from ai_core.service_response import info_message, error_message, warning_message
from pathlib import Path


logger = logging.getLogger(__name__)

# write - Create an item
def create_transcription_rating(item: TranscriptionRating, db: DatabaseAdapter, q: QueueClient, user: dict, request: Request):
    logger.info("===============create_transcription_rating called==============")

    # Get SQLite database instance from factory (will default to data/sqlite_db.sqlite3)
    sqlite_config = {"database_type": "sqlite"}
    sqlite_db = get_database(lambda: sqlite_config)

    item_id = item.id if hasattr(item, "id") and item.id else str(uuid.uuid4())
    logger.info(f"Using item_id: {item_id}")
    new_item = item.model_dump()
    new_item["id"] = item_id  # Store UUID in the database

    logger.info(item)

    # Use SQLite database instead of passed-in db
    sqlite_db.insert_item("transcription_rating", item_id, new_item)
    logger.info(f"TranscriptionRating created: {new_item}")
    if q:
        q.send_message(new_item)
        logger.info(f"Message sent to queue: TranscriptionRating created: {new_item}")
        logger.info(f"Queue message count: {q.get_message_count()}")
    return new_item

# read - get all items
def get_all_transcription_rating(db: DatabaseAdapter, user: dict, request: Request):
    logger.info("===============get_all_transcription_rating called==============")
    sqlite_config = {"database_type": "sqlite"}
    sqlite_db = get_database(lambda: sqlite_config)

    return sqlite_db.get_all_items("transcription_rating")

# read - get an item
def get_transcription_rating(id: str, db: DatabaseAdapter, user: dict, request: Request):
    logger.info("===============get_transcription_rating called==============")
    logger.info(f"Received request to retrieve transcription_rating with id: {id}")
    sqlite_config = {"database_type": "sqlite"}
    sqlite_db = get_database(lambda: sqlite_config)

    item = sqlite_db.get_item("transcription_rating", id)
    return item

# read - get_path an item
def get_path_transcription_rating(path: str, db: DatabaseAdapter, user: dict, request: Request):
    logger.info("===============get_transcription_rating called==============")
    logger.info(f"Received request to retrieve {path} with path: {path}")
    sqlite_config = {"database_type": "sqlite"}
    sqlite_db = get_database(lambda: sqlite_config)

    item = sqlite_db.get_item("transcription_rating", path)
    return item


# write - update an item (without modifying ID)
def update_transcription_rating(id: str, new_item: TranscriptionRating, db: DatabaseAdapter, q: QueueClient, user: dict, request: Request):
    logger.info("===============update_transcription_rating called==============")
    logger.info(new_item)
    sqlite_config = {"database_type": "sqlite"}
    sqlite_db = get_database(lambda: sqlite_config)

    sqlite_db.update_item("transcription_rating", id, new_item.model_dump())
    return sqlite_db.get_item("transcription_rating", id)

# write - delete an item
def delete_transcription_rating(id: str, db: DatabaseAdapter, q: QueueClient, user: dict, request: Request):
    logger.info("===============delete_transcription_rating called==============")
    logger.info(f"Received request to delete transcription_rating with id {id}")
    sqlite_config = {"database_type": "sqlite"}
    sqlite_db = get_database(lambda: sqlite_config)

    item = sqlite_config.get_item("transcription_rating", id)
    if not item:
        logger.warning(f"TranscriptionRating with id {id} not found")
        return None
    sqlite_config.delete_item("transcription_rating", id)
    return item