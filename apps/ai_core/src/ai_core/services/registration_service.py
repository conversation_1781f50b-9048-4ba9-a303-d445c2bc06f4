# FIXME - Request probably should not be here
# instead individual query parameters should be defined in the app.yml
# then put into a dictionary parameter to the service methods
from fastapi import Request
import logging
from typing import List
import uuid
from queues.interface import QueueClient
from database.interface import DatabaseAdapter
from ai_core.models.registration import Registration
from ai_core.service_response import info_message, error_message, warning_message
from pathlib import Path


logger = logging.getLogger(__name__)

# write - Create an item
def create_registration(item: Registration, db: DatabaseAdapter, q: QueueClient, user: dict, request: Request):
    logger.info("===============create_registration called==============")

    item_id = item.id if hasattr(item, "id") and item.id else str(uuid.uuid4())
    logger.info(f"Using item_id: {item_id}")
    new_item = item.model_dump()
    new_item["id"] = item_id  # Store UUID in the database

    logger.info(item)

    # FIXME - if db: ...
    db.insert_item("registration", item_id, new_item)
    logger.info(f"Registration created: {new_item}")
    if q:
        q.send_message(new_item)
        logger.info(f"Message sent to queue: Registration created: {new_item}")
        logger.info(f"Queue message count: {q.get_message_count()}")
    return new_item

# read - get all items
def get_all_registration(db: DatabaseAdapter, user: dict, request: Request):
    logger.info("===============get_all_registration called==============")
    return db.get_all_items("registration")

# read - get an item
def get_registration(id: str, db: DatabaseAdapter, user: dict, request: Request):
    logger.info("===============get_registration called==============")
    logger.info(f"Received request to retrieve registration with id: {id}")
    item = db.get_item("registration", id)
    return item

# read - get_path an item
def get_path_registration(path: str, db: DatabaseAdapter, user: dict, request: Request):
    logger.info("===============get_registration called==============")
    logger.info(f"Received request to retrieve {path} with path: {path}")
    item = db.get_item("registration", path)
    return item


# write - update an item (without modifying ID)
def update_registration(id: str, new_item: Registration, db: DatabaseAdapter, q: QueueClient, user: dict, request: Request):
    logger.info("===============update_registration called==============")
    logger.info(new_item)
    db.update_item("registration", id, new_item.model_dump())
    return db.get_item("registration", id)

# write - delete an item
def delete_registration(id: str, db: DatabaseAdapter, q: QueueClient, user: dict, request: Request):
    logger.info("===============delete_registration called==============")
    logger.info(f"Received request to delete registration with id {id}")
    item = db.get_item("registration", id)
    if not item:
        logger.warning(f"Registration with id {id} not found")
        return None
    db.delete_item("registration", id)
    return item