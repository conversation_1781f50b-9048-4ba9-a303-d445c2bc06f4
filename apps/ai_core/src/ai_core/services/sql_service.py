# FIXME - Request probably should not be here
# instead individual query parameters should be defined in the app.yml
# then put into a dictionary parameter to the service methods
from fastapi import Request
import logging
from typing import List
import uuid
from config import get_settings
from queues.interface import QueueClient
from database.interface import DatabaseAdapter
from ai_core.models.sql import Sql
from ai_core.service_response import info_message, error_message, warning_message
from pathlib import Path
from database.factory import get_database


logger = logging.getLogger(__name__)

# write - Create an item
def create_sql(item: Sql, db: DatabaseAdapter, q: QueueClient, user: dict, request: Request):
    logger.info("===============create_sql called==============")

    sqlite_config = {"database_type": "sqlite"}
    sqlite_db = get_database(lambda: sqlite_config)

    records = sqlite_db.query(item.sql)
    logger.info(f"Records: {records}")
    return { "id": item.id, "records": records }


# read - get all items
def get_all_sql(db: DatabaseAdapter, user: dict, request: Request):
    logger.info("===============get_all_sql called==============")
    return db.get_all_items("sql")

# read - get an item
def get_sql(id: str, db: DatabaseAdapter, user: dict, request: Request):
    logger.info("===============get_sql called==============")
    logger.info(f"Received request to retrieve sql with id: {id}")
    item = db.get_item("sql", id)
    return item

# read - get_path an item
def get_path_sql(path: str, db: DatabaseAdapter, user: dict, request: Request):
    logger.info("===============get_sql called==============")
    logger.info(f"Received request to retrieve {path} with path: {path}")
    item = db.get_item("sql", path)
    return item


# write - update an item (without modifying ID)
def update_sql(id: str, new_item: Sql, db: DatabaseAdapter, q: QueueClient, user: dict, request: Request):
    logger.info("===============update_sql called==============")
    logger.info(new_item)
    db.update_item("sql", id, new_item.model_dump())
    return db.get_item("sql", id)

# write - delete an item
def delete_sql(id: str, db: DatabaseAdapter, q: QueueClient, user: dict, request: Request):
    logger.info("===============delete_sql called==============")
    logger.info(f"Received request to delete sql with id {id}")
    item = db.get_item("sql", id)
    if not item:
        logger.warning(f"Sql with id {id} not found")
        return None
    db.delete_item("sql", id)
    return item