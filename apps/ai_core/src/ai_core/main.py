import logging
import json
import asyncio
import argparse
import os
from config import create_app_settings
from ai_core.models.config import Config as AiCoreConfig
from ai_core.pipeline import Pipeline
from ai_core.server import AppServer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

VERSION_FILE = "version.json"

def load_version():
    """Load the version from version.json safely."""
    try:
        if os.path.exists(VERSION_FILE):
            with open(VERSION_FILE, "r") as f:
                data = json.load(f)
                return data
    except (json.JSONDecodeError, IOError) as e:
        logger.warning(f"Error loading version.json: {e}")
    return {"version": "0.0.0"}

version = load_version()
logger.info(f"Loaded version: {version}")

def parse_args():
    parser = argparse.ArgumentParser(description="AI Core Main")
    parser.add_argument("--env_file", default=".env", help="Path to the .env file (default: .env)")
    return parser.parse_args()

async def run():

    args = parse_args()
    settings, _ = create_app_settings("ai_core", AiCoreConfig, env_file=args.env_file)
    settings: AiCoreConfig = settings

    if settings.pipeline_enabled and settings.app_server_enabled:
        pipeline = Pipeline(settings)
        app_server = AppServer(settings)
        await asyncio.gather(
            app_server.run(),
            pipeline.run(),
        )
    elif settings.pipeline_enabled:
        pipeline = Pipeline(settings)
        await pipeline.run()
    elif settings.app_server_enabled:
        app_server = AppServer(settings)
        await app_server.run()
    else:
        raise ValueError("No app server or pipeline enabled")

if __name__ == "__main__":
    asyncio.run(run())