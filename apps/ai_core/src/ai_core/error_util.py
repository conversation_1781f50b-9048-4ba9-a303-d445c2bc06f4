import logging
from fastapi import Request
from fastapi.responses import JSONResponse
from ai_core.models.config import Config as AiCoreConfig
from config import get_settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def log_exception(exc: Exception):
    """Logs the exception details."""
    logger.error(f"An error occurred: {exc}", exc_info=True)

def create_error_response(detail: str, status_code: int = 500):
    """Creates a standardized JSON error response."""
    settings: AiCoreConfig = get_settings()
    debug_mode = getattr(settings, "debug", False)
    message = detail if debug_mode else "An unexpected error occurred."
    return JSONResponse(
        status_code=status_code,
        content={"message": message},
    )