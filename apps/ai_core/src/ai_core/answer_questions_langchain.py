import logging
import openai
# from ai_core.slack import send_slack_message
from itertools import zip_longest
from concurrent.futures import ThreadPoolExecutor
import json
import jsonschema
import tempfile
from datetime import datetime
import io
import os
import time
import uuid
# from config import get_settings
from config import create_app_settings

from ai_core.models.config import Config as AiCoreConfig

# LangChain imports
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain_community.vectorstores import Chroma
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain.chains import RetrievalQA
from langchain.schema import Document
from langchain.prompts import PromptTemplate
from langchain.callbacks.base import BaseCallbackHand<PERSON>
from typing import Any, Dict, List, Optional
import chromadb
from dataclasses import dataclass

# Configuration Management
@dataclass
class RAGConfig:
    """Configuration constants for RAG system"""
    # Chunking parameters
    MAX_CHUNK_SIZE: int = 1000
    MAX_QUESTION_CHUNK_SIZE: int = 1500
    SEMANTIC_CHUNK_TURNS: int = 5

    # Retrieval parameters
    TOP_K_PER_TYPE: int = 3
    DEFAULT_RETRIEVAL_K: int = 5

    # Temporal thresholds
    EARLY_CUTOFF: float = 33.0
    LATE_CUTOFF: float = 67.0

    # LLM parameters
    DEFAULT_LLM_MODEL: str = "gpt-5-mini"
    TAGGING_LLM_MODEL: str = "gpt-5-mini"
    DEFAULT_TEMPERATURE: float = 0.0
    TAGGING_TEMPERATURE: float = 0.0
    MAX_TAGGING_TOKENS: int = 500

    # Embedding parameters
    DEFAULT_EMBEDDING_MODEL: str = "text-embedding-ada-002"

    # File output parameters
    ENABLE_CHUNK_ANALYSIS: bool = True
    ENABLE_READABLE_OUTPUT: bool = True
delete_openai_objects = False
number_of_question = 5

logger = logging.getLogger(__name__)

# Configure logging for better error tracking
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Constants - same as original
INSTRUCTIONS = """
You are an expert system designed to extract relevant information/answer from conversations between clinician and patient.
Given a set of questions from the user, you will analyze the transcript and provide concise answers to the user's questions based on the information present in the conversation.
If the question is not found in the conversation, the default answer will be "Not Available"
"""

user_content_default = """
### Objective
{{role_injection}}
Extract answers from the conversation transcript for each question listed below. Base your responses ONLY on information present in the conversation. Use ["Not Available"] for any question without a clear answer in the transcript.

Today's Date: {{todays_date}}

### Instructions:
1. Extract information ONLY from the provided conversation.
2. For each question, provide the exact relevant information without adding assumptions.
3. Use the format specified in the Output section.
4. Return ["Not Available"] when information cannot be found.
5. Select appropriate options from the provided choices when applicable.


### Input Format
Each question has this format:
- question_code: Unique identifier
- question: The text prompt
- question_type: Format type (radio-group, select-drop-down, checklist, checkbox, date-field, multiline-text-field)
- labelName: Display label
- section: Category
- options: Available choices (when applicable)

### Response Format Rules
- radio-group, select-drop-down, checkbox, date-field, multiline-text-field: Single string in answer_text array
- checklist: Can have multiple strings in answer_text array when multiple options apply
- All responses must include question_code, question_text, answer_context,answer_reason and answer_text

### Output Schema (JSON only)
{
  "question_code": "[ID from input]",
  "question_type": "[Question Type for RPA]",
  "question_code": "[ID from input]",
  "answer_context": ["[Patient's exact sentence from transcript]"],
  "answer_reason":["your thoughts on why this answer is choosed in 2 lines and include which chunk you choose and why you choose that chunk"],
  "answer_text": ["[Selected option or extracted answer]"]
}

### Example
For a radio-group question about feeding ability where the transcript shows "I can feed myself":
{
  "question_code": "M1870",
  "question_type": "radio-group",
  "question_text": "Current ability to feed self meals and snacks safely",
  "answer_context": ["I can feed myself."],
  "answer_text": ["0 - Able to independently feed self."]
}

## Questions to Answer:
{{question_list_in_json}}
"""

# Updated schemas to include answer_reason
response_schema = {
    "type": "array",
    "items": {
        "type": "object",
        "required": ["question_code", "question_text", "question_type", "answer_context", "answer_reason", "answer_text"],
        "properties": {
            "question_code": {"type": "string"},
            "question_text": {"type": "string"},
            "question_type": {"type": "string"},
            "answer_context": {
                "type": "array",
                "items": {"type": "string"}
            },
            "answer_reason": {
                "type": "array",
                "items": {"type": "string"}
            },
            "answer_text": {
                "type": "array",
                "items": {"type": "string"}
            }
        },
        "additionalProperties": False
    }
}

single_response_schema = {
    "type": "object",
    "required": ["question_code", "question_text", "question_type", "answer_context", "answer_reason", "answer_text"],
    "properties": {
        "question_code": {"type": "string"},
        "question_text": {"type": "string"},
        "question_type": {"type": "string"},
        "answer_context": {
            "type": "array",
            "items": {"type": "string"}
        },
        "answer_reason": {
            "type": "array",
            "items": {"type": "string"}
        },
        "answer_text": {
            "type": "array",
            "items": {"type": "string"}
        }
    },
    "additionalProperties": False
}

class RetrievalDebugHandler(BaseCallbackHandler):
    """Custom callback handler to capture retrieval information"""

    def __init__(self):
        self.retrieved_docs = []
        self.query = ""
        self.llm_input = ""
        self.llm_output = ""

    def on_retriever_start(self, serialized: Dict[str, Any], query: str, **kwargs: Any) -> None:
        """Called when retriever starts"""
        self.query = query
        print(f"\n🔍 STARTING RETRIEVAL...")
        print(f"Looking for relevant conversation text to answer your questions...",query)

    def on_retriever_end(self, documents: List[Document], **kwargs: Any) -> None:
        """Called when retriever ends"""
        self.retrieved_docs = documents
        print(f"\n{'='*80}")
        print("📄 RETRIEVED CONTEXT FROM CONVERSATION")
        print(f"{'='*80}")
        print(f"Found {len(documents)} relevant text chunks from the conversation:")

        # Show chunk type distribution
        chunk_types = {}
        for doc in documents:
            chunk_type = doc.metadata.get('chunk_type', 'unknown')
            chunk_types[chunk_type] = chunk_types.get(chunk_type, 0) + 1

        print(f"\n📊 Chunk Type Distribution:")
        for chunk_type, count in chunk_types.items():
            print(f"   {chunk_type}: {count} chunks")

        for i, doc in enumerate(documents, 1):
            chunk_type = doc.metadata.get('chunk_type', 'unknown')
            print(f"\n📋 Retrieved Text Chunk {i} ({chunk_type}):")
            print(f"{'-'*50}")
            # Clean up the JSON formatting to make it more readable
            content = doc.page_content
            try:
                # Try to parse and reformat if it's JSON
                import json
                parsed = json.loads(content)
                if isinstance(parsed, list):
                    for j, item in enumerate(parsed):
                        if isinstance(item, dict) and 'text' in item:
                            speaker = f"Speaker {item.get('speaker_id', 'Unknown')}"
                            text = item['text']
                            print(f"🗣️  {speaker}: {text}")
                        else:
                            print(f"📝 Item {j+1}: {item}")
                else:
                    print(content)
            except:
                # If not JSON, just print as is but try to make it more readable
                # Remove excessive JSON formatting characters
                clean_content = content.replace('",\n    "', '"\n"').replace('{\n    "', '{"')
                print(clean_content)
            print(f"{'-'*50}")

    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs: Any) -> None:
        """Called when LLM starts"""
        if prompts:
            self.llm_input = prompts[0]
            print(f"\n🔄 SENDING TO LLM:")
            print(f"{'-'*50}")
            print("The above retrieved text chunks are now being sent to the LLM along with your questions...")
            print(f"{'-'*50}")

    def on_llm_end(self, response, **kwargs: Any) -> None:
        """Called when LLM ends"""
        if hasattr(response, 'generations') and response.generations:
            self.llm_output = response.generations[0][0].text
            print(f"\n🤖 LLM RESPONSE:")
            print(f"{'-'*50}")
            print(f"{self.llm_output}")

        print(f"\n{'='*80}\n")


class LangChainRAG:
    """LangChain-based RAG implementation"""
    
    def __init__(self, embedding_model: str = "text-embedding-ada-002", openai_api_key: str = None):
        """
        Initialize LangChain RAG system
        
        Args:
            embedding_model: OpenAI embedding model to use
            openai_api_key: OpenAI API key
        """
        # self.embedding_model = embedding_model
        self.embedding_model = "emilyalsentzer/Bio_ClinicalBERT"
        self.openai_api_key = openai_api_key
        self.embeddings = None
        self.vectorstore = None
        self.retriever = None
        self.qa_chain = None
        self.debug_handler = RetrievalDebugHandler()
        
        # Initialize embeddings
        self._initialize_embeddings()
    
    def _initialize_embeddings(self):
        """Initialize the embedding model"""
        print(f"🔧 Initializing embeddings: {self.embedding_model}")

        # OpenAI embedding models
        openai_models = [
            "text-embedding-ada-002",
            "text-embedding-3-small",
            "text-embedding-3-large"
        ]

        # Hugging Face embedding models
        huggingface_models = [
            "emilyalsentzer/Bio_ClinicalBERT",
            "michiyasunaga/BioLinkBERT-large",
            "sentence-transformers/all-MiniLM-L6-v2",
            "sentence-transformers/all-mpnet-base-v2",
            "microsoft/BiomedNLP-PubMedBERT-base-uncased-abstract-fulltext"
        ]

        if self.embedding_model in openai_models:
            print("📡 Using OpenAI embeddings")
            self.embeddings = OpenAIEmbeddings(
                model=self.embedding_model,
                openai_api_key=self.openai_api_key
            )
        elif self.embedding_model in huggingface_models:
            print("🤗 Using Hugging Face embeddings")
            self.embeddings = HuggingFaceEmbeddings(
                model_name=self.embedding_model,
                model_kwargs={'device': 'cpu'},  # Use CPU for compatibility
                encode_kwargs={'normalize_embeddings': True}
            )
        else:
            # Auto-detect based on model name pattern
            if self.embedding_model.startswith("text-embedding"):
                print("📡 Auto-detected OpenAI embeddings")
                self.embeddings = OpenAIEmbeddings(
                    model=self.embedding_model,
                    openai_api_key=self.openai_api_key
                )
            else:
                print("🤗 Auto-detected Hugging Face embeddings")
                self.embeddings = HuggingFaceEmbeddings(
                    model_name=self.embedding_model,
                    model_kwargs={'device': 'cpu'},
                    encode_kwargs={'normalize_embeddings': True}
                )
    
    def _create_semantic_chunks(self, conversation_data: dict, client_id: str, max_chunk_size: int = None):
        """
        Create semantic chunks from conversation data based on dialogue turns

        Args:
            conversation_data: The conversation data (list of speaker turns)
            client_id: Client identifier
            max_chunk_size: Maximum characters per chunk (uses config default if None)

        Returns:
            List of Document objects with semantically meaningful chunks
        """
        if max_chunk_size is None:
            max_chunk_size = RAGConfig.MAX_CHUNK_SIZE

        chunks = []
        current_chunk = []
        current_chunk_size = 0
        chunk_index = 0

        # Handle both list and dict formats
        if isinstance(conversation_data, dict):
            # If it's a dict, look for a list inside it
            conversation_list = conversation_data.get('conversation', conversation_data.get('data', []))
            if not isinstance(conversation_list, list):
                # If still not a list, convert the whole dict to a single item list
                conversation_list = [conversation_data]
        else:
            conversation_list = conversation_data

        chunk_start_turn = 0  # Track where current chunk starts

        for turn_idx, turn in enumerate(conversation_list):
            if not isinstance(turn, dict) or 'text' not in turn:
                continue

            speaker_id = turn.get('speaker_id', 'unknown')
            text = turn.get('text', '')

            # Format the turn for better readability
            speaker_label = "CLINICIAN" if speaker_id == 0 else "PATIENT" if speaker_id == 1 else f"SPEAKER_{speaker_id}"
            formatted_turn = f"{speaker_label}: {text}"

            # Calculate size if we add this turn
            turn_size = len(formatted_turn) + 1  # +1 for newline

            # If adding this turn would exceed max size and we have content, create a chunk
            if current_chunk and (current_chunk_size + turn_size > max_chunk_size):
                chunk_text = "\n".join(current_chunk)

                # Calculate temporal information
                total_turns = len(conversation_list)
                temporal_position = (chunk_start_turn + len(current_chunk) / 2) / total_turns * 100

                if temporal_position < RAGConfig.EARLY_CUTOFF:
                    relative_timing = "EARLY"
                elif temporal_position < RAGConfig.LATE_CUTOFF:
                    relative_timing = "MIDDLE"
                else:
                    relative_timing = "LATE"

                chunk_metadata = {
                    "client_id": client_id,
                    "chunk_index": chunk_index,
                    "turn_count": len(current_chunk),
                    "chunk_type": "dialogue_turns",
                    # Temporal information
                    "conversation_order": chunk_index + 1,
                    "start_turn_index": chunk_start_turn,
                    "end_turn_index": chunk_start_turn + len(current_chunk) - 1,
                    "temporal_position": round(temporal_position, 1),
                    "relative_timing": relative_timing
                }

                chunks.append(Document(
                    page_content=chunk_text,
                    metadata=chunk_metadata
                ))

                # Reset for next chunk
                chunk_start_turn = turn_idx
                current_chunk = []
                current_chunk_size = 0
                chunk_index += 1

            # Add the current turn to the chunk
            current_chunk.append(formatted_turn)
            current_chunk_size += turn_size

            # If we have 5 turns, consider creating a chunk (even if under max size)
            if len(current_chunk) >= 5:
                chunk_text = "\n".join(current_chunk)

                # Calculate temporal information
                total_turns = len(conversation_list)
                temporal_position = (chunk_start_turn + len(current_chunk) / 2) / total_turns * 100

                if temporal_position < RAGConfig.EARLY_CUTOFF:
                    relative_timing = "EARLY"
                elif temporal_position < RAGConfig.LATE_CUTOFF:
                    relative_timing = "MIDDLE"
                else:
                    relative_timing = "LATE"

                chunk_metadata = {
                    "client_id": client_id,
                    "chunk_index": chunk_index,
                    "turn_count": len(current_chunk),
                    "chunk_type": "dialogue_turns",
                    # Temporal information
                    "conversation_order": chunk_index + 1,
                    "start_turn_index": chunk_start_turn,
                    "end_turn_index": chunk_start_turn + len(current_chunk) - 1,
                    "temporal_position": round(temporal_position, 1),
                    "relative_timing": relative_timing
                }

                chunks.append(Document(
                    page_content=chunk_text,
                    metadata=chunk_metadata
                ))

                # Reset for next chunk
                chunk_start_turn = turn_idx + 1
                current_chunk = []
                current_chunk_size = 0
                chunk_index += 1

        # Don't forget the last chunk if it has content
        if current_chunk:
            chunk_text = "\n".join(current_chunk)

            # Calculate temporal information for final chunk
            total_turns = len(conversation_list)
            temporal_position = (chunk_start_turn + len(current_chunk) / 2) / total_turns * 100

            if temporal_position < RAGConfig.EARLY_CUTOFF:
                relative_timing = "EARLY"
            elif temporal_position < RAGConfig.LATE_CUTOFF:
                relative_timing = "MIDDLE"
            else:
                relative_timing = "LATE"

            chunk_metadata = {
                "client_id": client_id,
                "chunk_index": chunk_index,
                "turn_count": len(current_chunk),
                "chunk_type": "dialogue_turns",
                # Temporal information
                "conversation_order": chunk_index + 1,
                "start_turn_index": chunk_start_turn,
                "end_turn_index": chunk_start_turn + len(current_chunk) - 1,
                "temporal_position": round(temporal_position, 1),
                "relative_timing": relative_timing
            }

            chunks.append(Document(
                page_content=chunk_text,
                metadata=chunk_metadata
            ))

        return chunks

    def _create_blind_chunks(self, conversation_data: dict, client_id: str, chunk_size: int = 1000, chunk_overlap: int = 200):
        """
        Create blind chunks from conversation data using character-based splitting

        Args:
            conversation_data: The conversation data
            client_id: Client identifier
            chunk_size: Maximum characters per chunk
            chunk_overlap: Overlap between chunks

        Returns:
            List of Document objects with character-based chunks
        """
        from langchain.text_splitter import RecursiveCharacterTextSplitter

        # Convert conversation data to text
        conversation_text = json.dumps(conversation_data, indent=2)

        # Split text into chunks
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
        )

        # Create documents
        documents = [Document(
            page_content=conversation_text,
            metadata={
                "client_id": client_id,
                "chunk_type": "blind_character_split"
            }
        )]
        blind_chunks = text_splitter.split_documents(documents)

        # Update metadata for blind chunks with temporal information
        total_chunks = len(blind_chunks)
        for i, chunk in enumerate(blind_chunks):
            # Calculate temporal position for blind chunks
            temporal_position = (i + 0.5) / total_chunks * 100

            if temporal_position < RAGConfig.EARLY_CUTOFF:
                relative_timing = "EARLY"
            elif temporal_position < RAGConfig.LATE_CUTOFF:
                relative_timing = "MIDDLE"
            else:
                relative_timing = "LATE"

            chunk.metadata.update({
                "client_id": client_id,
                "chunk_index": i,
                "chunk_type": "blind_character_split",
                "chunk_size": len(chunk.page_content),
                # Temporal information
                "conversation_order": i + 1,
                "temporal_position": round(temporal_position, 1),
                "relative_timing": relative_timing
            })

        return blind_chunks

    def _create_question_aware_chunks(self, conversation_data: dict, client_id: str, max_chunk_size: int = 1500):
        """
        Create chunks that specifically capture question-answer patterns

        Args:
            conversation_data: The conversation data
            client_id: Client identifier
            max_chunk_size: Maximum characters per chunk

        Returns:
            List of Document objects with question-answer focused chunks
        """
        chunks = []
        chunk_index = 0

        # Handle both list and dict formats
        if isinstance(conversation_data, dict):
            conversation_list = conversation_data.get('conversation', conversation_data.get('data', []))
            if not isinstance(conversation_list, list):
                conversation_list = [conversation_data]
        else:
            conversation_list = conversation_data

        # Look for question patterns and group with following responses
        question_keywords = [
            'what', 'how', 'when', 'where', 'why', 'who', 'which', 'can you', 'are you', 'do you',
            'tell me', 'show me', 'repeat', 'recall', 'remember', 'three words', 'words that I asked'
        ]

        i = 0
        while i < len(conversation_list):
            current_chunk = []
            current_chunk_size = 0

            # Start from current position
            start_idx = i

            # Look for questions in the next few turns
            for j in range(i, min(i + 10, len(conversation_list))):  # Look ahead up to 10 turns
                turn = conversation_list[j]
                if not isinstance(turn, dict) or 'text' not in turn:
                    continue

                text = turn.get('text', '').lower()
                speaker_id = turn.get('speaker_id', 'unknown')

                # Check if this turn contains a question
                is_question = any(keyword in text for keyword in question_keywords) or text.strip().endswith('?')

                if is_question and speaker_id == 0:  # Clinician asking question
                    # Found a question, now collect this question + following responses
                    question_chunk = []
                    question_chunk_size = 0

                    # Add the question and several following turns
                    for k in range(j, min(j + 8, len(conversation_list))):  # Include question + next 7 turns
                        turn_k = conversation_list[k]
                        if not isinstance(turn_k, dict) or 'text' not in turn_k:
                            continue

                        speaker_k = turn_k.get('speaker_id', 'unknown')
                        text_k = turn_k.get('text', '')

                        speaker_label = "CLINICIAN" if speaker_k == 0 else "PATIENT" if speaker_k == 1 else f"SPEAKER_{speaker_k}"
                        formatted_turn = f"{speaker_label}: {text_k}"
                        turn_size = len(formatted_turn) + 1

                        if question_chunk_size + turn_size > max_chunk_size and question_chunk:
                            break

                        question_chunk.append(formatted_turn)
                        question_chunk_size += turn_size

                    # Create chunk if we have content
                    if question_chunk:
                        chunk_text = "\n".join(question_chunk)

                        # Calculate temporal information
                        total_turns = len(conversation_list)
                        start_turn = j
                        end_turn = min(j + len(question_chunk) - 1, total_turns - 1)
                        temporal_position = (start_turn + end_turn) / 2 / total_turns * 100

                        if temporal_position < RAGConfig.EARLY_CUTOFF:
                            relative_timing = "EARLY"
                        elif temporal_position < RAGConfig.LATE_CUTOFF:
                            relative_timing = "MIDDLE"
                        else:
                            relative_timing = "LATE"

                        chunk_metadata = {
                            "client_id": client_id,
                            "chunk_index": chunk_index,
                            "turn_count": len(question_chunk),
                            "chunk_type": "question_answer_focused",
                            "contains_question": True,
                            # Temporal information
                            "conversation_order": chunk_index + 1,
                            "start_turn_index": start_turn,
                            "end_turn_index": end_turn,
                            "temporal_position": round(temporal_position, 1),
                            "relative_timing": relative_timing
                        }

                        chunks.append(Document(
                            page_content=chunk_text,
                            metadata=chunk_metadata
                        ))
                        chunk_index += 1

                    # Move past this question sequence
                    i = min(j + 5, len(conversation_list))
                    break
            else:
                # No question found in this window, move forward
                i += 3

        return chunks

    def _create_memory_test_chunks(self, conversation_data: dict, client_id: str):
        """
        Create specific chunks for memory test sequences to ensure they're always captured
        """
        chunks = []
        chunk_index = 0

        # Handle both list and dict formats
        if isinstance(conversation_data, dict):
            conversation_list = conversation_data.get('conversation', conversation_data.get('data', []))
            if not isinstance(conversation_list, list):
                conversation_list = [conversation_data]
        else:
            conversation_list = conversation_data

        # Look for memory test patterns
        memory_keywords = [
            'three words', 'remember', 'repeat the words', 'memory test',
            'sock, blue, and bed', 'gonna say three words'
        ]

        for i, turn in enumerate(conversation_list):
            if not isinstance(turn, dict) or 'text' not in turn:
                continue

            text = turn.get('text', '').lower()

            # Check if this turn contains memory test content
            if any(keyword in text for keyword in memory_keywords):
                # Create a chunk with this turn plus surrounding context
                context_chunk = []

                # Include 3 turns before and 5 turns after for full context
                start_idx = max(0, i - 3)
                end_idx = min(len(conversation_list), i + 6)

                for j in range(start_idx, end_idx):
                    context_turn = conversation_list[j]
                    if not isinstance(context_turn, dict) or 'text' not in context_turn:
                        continue

                    speaker_id = context_turn.get('speaker_id', 'unknown')
                    text_content = context_turn.get('text', '')

                    speaker_label = "CLINICIAN" if speaker_id == 0 else "PATIENT" if speaker_id == 1 else f"SPEAKER_{speaker_id}"
                    formatted_turn = f"{speaker_label}: {text_content}"
                    context_chunk.append(formatted_turn)

                if context_chunk:
                    chunk_text = "\n".join(context_chunk)

                    # Calculate temporal information
                    total_turns = len(conversation_list)
                    start_turn = start_idx
                    end_turn = end_idx - 1
                    temporal_position = (start_turn + end_turn) / 2 / total_turns * 100

                    if temporal_position < RAGConfig.EARLY_CUTOFF:
                        relative_timing = "EARLY"
                    elif temporal_position < RAGConfig.LATE_CUTOFF:
                        relative_timing = "MIDDLE"
                    else:
                        relative_timing = "LATE"

                    chunk_metadata = {
                        "client_id": client_id,
                        "chunk_index": chunk_index,
                        "turn_count": len(context_chunk),
                        "chunk_type": "memory_test_focused",
                        "contains_memory_test": True,
                        # Temporal information
                        "conversation_order": chunk_index + 1,
                        "start_turn_index": start_turn,
                        "end_turn_index": end_turn,
                        "temporal_position": round(temporal_position, 1),
                        "relative_timing": relative_timing
                    }

                    chunks.append(Document(
                        page_content=chunk_text,
                        metadata=chunk_metadata
                    ))
                    chunk_index += 1

        return chunks

    def create_vectorstore_from_conversation(self, conversation_data: dict, client_id: str, questions: list = None):
        """
        Create vector store from conversation data using semantic, blind, and question-aware chunking

        Args:
            conversation_data: The conversation data to vectorize
            client_id: Client identifier for the vector store
            questions: Optional list of OASIS questions for tagging chunks with question codes
        """
        print(f"🔄 Creating semantic, blind, question-aware, and memory-test chunks for conversation...")

        # Create semantic chunks based on dialogue turns
        semantic_chunks = self._create_semantic_chunks(conversation_data, client_id)
        print(f"📝 Created {len(semantic_chunks)} semantic chunks")

        # Create blind chunks using character-based splitting
        blind_chunks = self._create_blind_chunks(conversation_data, client_id)
        print(f"📄 Created {len(blind_chunks)} blind chunks")

        # Create question-aware chunks to capture Q&A patterns
        question_chunks = self._create_question_aware_chunks(conversation_data, client_id)
        print(f"❓ Created {len(question_chunks)} question-aware chunks")

        # Combine all types of chunks
        all_chunks = semantic_chunks + blind_chunks + question_chunks

        # print(f"🔗 Total chunks: {len(all_chunks)} ({len(semantic_chunks)} semantic + {len(blind_chunks)} blind + {len(question_chunks)} question-aware + {len(memory_chunks)} memory-test)")

        # Tag chunks with OASIS question codes if questions are provided
        if questions:
            print(f"🏷️  Starting OASIS question code tagging for improved retrieval...")
            all_chunks = tag_chunks_with_question_codes(all_chunks, questions, self.openai_api_key)
        else:
            print(f"ℹ️  No questions provided - skipping question code tagging")

        # Show summary of chunks
        for i, chunk in enumerate(all_chunks[:6]):  # Show first 6 chunks as example
            chunk_type = chunk.metadata.get('chunk_type', 'unknown')
            chunk_size = len(chunk.page_content)

            # Get question codes - try list first, then string format
            question_codes = chunk.metadata.get('question_codes_list', [])
            if not question_codes:
                question_codes_str = chunk.metadata.get('question_codes', '')
                question_codes = [code.strip() for code in question_codes_str.split(',') if code.strip()]

            # Get temporal information
            conversation_order = chunk.metadata.get('conversation_order', 'N/A')
            temporal_position = chunk.metadata.get('temporal_position', 'N/A')
            relative_timing = chunk.metadata.get('relative_timing', 'unknown')

            if chunk_type in ["dialogue_turns", "question_answer_focused", "memory_test_focused"]:
                start_turn = chunk.metadata.get('start_turn_index', 'N/A')
                end_turn = chunk.metadata.get('end_turn_index', 'N/A')

                if question_codes:
                    print(f"   Chunk {i+1}: {chunk_type} - 🕐 Order: {conversation_order} ({temporal_position}% - {relative_timing}) - Turns {start_turn}-{end_turn}, {chunk_size} chars, tagged with {len(question_codes)} codes")
                else:
                    print(f"   Chunk {i+1}: {chunk_type} - 🕐 Order: {conversation_order} ({temporal_position}% - {relative_timing}) - Turns {start_turn}-{end_turn}, {chunk_size} chars")
            else:
                if question_codes:
                    print(f"   Chunk {i+1}: {chunk_type} - 🕐 Order: {conversation_order} ({temporal_position}% - {relative_timing}) - {chunk_size} chars, tagged with {len(question_codes)} codes")
                else:
                    print(f"   Chunk {i+1}: {chunk_type} - 🕐 Order: {conversation_order} ({temporal_position}% - {relative_timing}) - {chunk_size} chars")

        if len(all_chunks) > 6:
            print(f"   ... and {len(all_chunks) - 6} more chunks")

        # Filter complex metadata (remove lists) before creating vector store
        filtered_chunks = []
        for chunk in all_chunks:
            # Store the original question_codes_list for internal use
            original_codes = chunk.metadata.get('question_codes_list', [])

            # Create a new metadata dict without complex types
            filtered_metadata = {}
            for key, value in chunk.metadata.items():
                # Skip list values (Chroma doesn't support them)
                if not isinstance(value, (list, dict, tuple, set)):
                    filtered_metadata[key] = value

            # Store the original codes as a string (Chroma doesn't support lists)
            filtered_metadata['_original_question_codes'] = ",".join(original_codes) if original_codes else ""

            # Create new Document with filtered metadata
            filtered_chunk = Document(
                page_content=chunk.page_content,
                metadata=filtered_metadata
            )
            filtered_chunks.append(filtered_chunk)

        # Create vector store using Chroma (in-memory)
        self.vectorstore = Chroma.from_documents(
            documents=filtered_chunks,
            embedding=self.embeddings,
            collection_name=f"conversation_{client_id}_{int(time.time())}"
        )

        # Create retriever with MMR (Maximum Marginal Relevance) for diversity
        self.retriever = self.vectorstore.as_retriever(
            search_type="mmr",  # Use MMR to get diverse chunks (mix of semantic, blind, Q&A)
            search_kwargs={
                "k": 3,  # Retrieve more chunks to capture complete memory test sequence
                "lambda_mult": 0.5,  # More diversity to get different parts of conversation
                "fetch_k": 24  # Fetch many more candidates to find initial questions
            }
        )

        return self.vectorstore

    def retrieve_chunks_by_question_codes(self, question_codes: List[str], k: int = 5) -> List[Document]:
        """
        Retrieve chunks that are tagged with specific question codes

        Args:
            question_codes: List of question codes to search for
            k: Maximum number of chunks to retrieve

        Returns:
            List of Document objects that contain the specified question codes
        """
        if not self.vectorstore:
            return []

        # Get all documents from the vector store
        all_docs = self.vectorstore.get()

        # Filter documents that contain any of the specified question codes
        matching_docs = []
        for i, doc_metadata in enumerate(all_docs.get('metadatas', [])):
            # Get question codes using the helper method
            doc_question_codes = self._get_question_codes_from_metadata(doc_metadata)

            if any(code in doc_question_codes for code in question_codes):
                # Reconstruct the Document object
                doc = Document(
                    page_content=all_docs['documents'][i],
                    metadata=doc_metadata
                )
                matching_docs.append(doc)

        # Sort by relevance (number of matching question codes) and return top k
        matching_docs.sort(
            key=lambda doc: len(set(self._get_question_codes_from_metadata(doc.metadata)) & set(question_codes)),
            reverse=True
        )

        return matching_docs[:k]

    def enhanced_retrieve(self, query: str, question_codes: List[str] = None, k: int = 5) -> List[Document]:
        """
        Enhanced retrieval that combines semantic search with question code filtering

        Args:
            query: The search query
            question_codes: Optional list of question codes to prioritize
            k: Number of documents to retrieve

        Returns:
            List of Document objects combining semantic and code-based retrieval
        """
        if not self.retriever:
            return []

        # Get semantic retrieval results
        semantic_docs = self.retriever.get_relevant_documents(query)

        # If question codes are provided, also get code-based results
        if question_codes:
            code_based_docs = self.retrieve_chunks_by_question_codes(question_codes, k)

            # Combine and deduplicate results
            all_docs = semantic_docs + code_based_docs
            seen_content = set()
            unique_docs = []

            for doc in all_docs:
                if doc.page_content not in seen_content:
                    seen_content.add(doc.page_content)
                    unique_docs.append(doc)

            return unique_docs[:k]
        else:
            return semantic_docs[:k]

    def retrieve_by_chunk_types(self, query: str, top_k_per_type: int = None) -> List[Document]:
        """
        Retrieve top K chunks from each chunk type separately

        Args:
            query: The search query
            top_k_per_type: Number of chunks to retrieve from each type (uses config default if None)

        Returns:
            List of Document objects with top chunks from each type
        """
        if top_k_per_type is None:
            top_k_per_type = RAGConfig.TOP_K_PER_TYPE

        if not self.vectorstore:
            return []

        # Get all documents from vector store
        all_data = self.vectorstore.get()
        documents = all_data.get('documents', [])
        metadatas = all_data.get('metadatas', [])

        # Group documents by chunk type
        chunk_types = {
            'dialogue_turns': [],
            'blind_character_split': [],
            'question_answer_focused': [],
            'memory_test_focused': []
        }

        for doc_content, metadata in zip(documents, metadatas):
            if not metadata:
                continue

            chunk_type = metadata.get('chunk_type', 'unknown')
            if chunk_type in chunk_types:
                doc = Document(
                    page_content=doc_content,
                    metadata=metadata
                )
                chunk_types[chunk_type].append(doc)

        # Get embeddings for the query
        query_embedding = self.embeddings.embed_query(query)

        # For each chunk type, calculate similarity and get top K
        selected_chunks = []

        for chunk_type, chunks in chunk_types.items():
            if not chunks:
                continue

            # Calculate similarity scores for chunks of this type
            chunk_scores = []
            for chunk in chunks:
                # Get chunk embedding
                chunk_embedding = self.embeddings.embed_query(chunk.page_content)

                # Calculate cosine similarity
                import numpy as np
                similarity = np.dot(query_embedding, chunk_embedding) / (
                    np.linalg.norm(query_embedding) * np.linalg.norm(chunk_embedding)
                )
                chunk_scores.append((chunk, similarity))

            # Sort by similarity and take top K
            chunk_scores.sort(key=lambda x: x[1], reverse=True)
            top_chunks = [chunk for chunk, _ in chunk_scores[:top_k_per_type]]

            # Add chunk type header for LLM context
            for chunk in top_chunks:
                chunk_type_header = f"[CHUNK TYPE: {chunk_type.upper()}]"
                chunk.page_content = chunk_type_header + "\n" + chunk.page_content

            selected_chunks.extend(top_chunks)

            print(f"📋 Selected {len(top_chunks)} chunks from {chunk_type}")

        # Sort all selected chunks by temporal order for chronological context
        selected_chunks.sort(key=lambda x: x.metadata.get('conversation_order', 0))

        return selected_chunks

    def setup_qa_chain(self, llm_model: str = None, temperature: float = None):
        """
        Setup the QA chain with the retriever

        Args:
            llm_model: The LLM model to use (uses config default if None)
            temperature: Temperature for the LLM (uses config default if None)
        """
        if llm_model is None:
            llm_model = RAGConfig.DEFAULT_LLM_MODEL
        if temperature is None:
            temperature = RAGConfig.DEFAULT_TEMPERATURE

        if not self.retriever:
            raise ValueError("Retriever not initialized. Call create_vectorstore_from_conversation first.")

        # Initialize LLM
        llm = ChatOpenAI(
            # model=llm_model,
            model="gpt-4o",
            temperature=temperature,
            openai_api_key=self.openai_api_key
        )

        # Create custom prompt template
        prompt_template = PromptTemplate(
            input_variables=["context", "question"],
            template="""Use the following pieces of context to answer the question. If you don't know the answer based on the context, just say "Not Available".

Context: {context}

Question: {question}

Answer:"""
        )

        # Create QA chain
        self.qa_chain = RetrievalQA.from_chain_type(
            llm=llm,
            chain_type="stuff",
            retriever=self.retriever,
            chain_type_kwargs={"prompt": prompt_template},
            callbacks=[self.debug_handler],
            return_source_documents=True
        )
    
    def extract_question_codes_from_query(self, query: str) -> List[str]:
        """
        Extract question codes from a query string that contains JSON questions

        Args:
            query: The query string that may contain JSON with question codes

        Returns:
            List of question codes found in the query
        """
        question_codes = []
        try:
            # Look for JSON content in the query
            if "## Questions to Answer:" in query:
                json_part = query.split("## Questions to Answer:")[1].strip()
                questions = json.loads(json_part)

                if isinstance(questions, list):
                    for q in questions:
                        if isinstance(q, dict) and "question_code" in q:
                            question_codes.append(q["question_code"])

        except (json.JSONDecodeError, KeyError, IndexError):
            # If we can't parse JSON, try to find question codes with regex
            import re
            # Look for patterns like M1800, M1028, etc.
            pattern = r'\b[A-Z]\d{4}\b'
            matches = re.findall(pattern, query)
            question_codes.extend(matches)

        return list(set(question_codes))  # Remove duplicates

    def _get_question_codes_from_metadata(self, metadata: dict) -> List[str]:
        """
        Helper method to extract question codes from metadata, handling both list and string formats

        Args:
            metadata: Document metadata dictionary

        Returns:
            List of question codes
        """
        if not metadata:
            return []

        # Try original codes first (stored during vector store creation as string)
        original_codes_str = metadata.get('_original_question_codes', '')
        if original_codes_str:
            return [code.strip() for code in original_codes_str.split(',') if code.strip()]

        # Try list format (for backward compatibility)
        question_codes = metadata.get('question_codes_list', [])
        if question_codes:
            return question_codes

        # Fallback to string format
        question_codes_str = metadata.get('question_codes', '')
        if question_codes_str:
            return [code.strip() for code in question_codes_str.split(',') if code.strip()]

        return []

    def query(self, question: str) -> dict:
        """
        Query the RAG system with enhanced retrieval using question codes

        Args:
            question: The question to ask

        Returns:
            Dictionary containing the answer and source documents
        """
        if not self.qa_chain:
            raise ValueError("QA chain not initialized. Call setup_qa_chain first.")

        # Reset debug handler for this query
        self.debug_handler = RetrievalDebugHandler()

        # Extract question codes from the query for enhanced retrieval
        question_codes = self.extract_question_codes_from_query(question)

        if question_codes:
            print(f"🔍 Detected question codes in query: {question_codes}")
            print(f"🔍 Using enhanced retrieval with question code filtering...")

        # Enhance the query for medical context and related questions
        # Special handling for memory test questions
        if "three words" in question.lower() or "repeat" in question.lower() or "recall" in question.lower():
            enhanced_query = f"""Memory test analysis: {question}

            Find ALL parts of the memory test including:
            - Initial instruction: "I'm going to say three words"
            - The original words given: "sock, blue, bed"
            - Patient's immediate repetition
            - Later recall attempts
            - Any follow-up questions about specific words

            Search for: three words, remember, repeat, recall, sock, blue, bed, memory test

            Target: {question}"""
        else:
            enhanced_query = f"""Medical conversation analysis for: {question}

            Search for relevant information including:
            - Direct answers and responses
            - Related questions from earlier dialogue
            - Follow-up clarifications
            - Conversational context and flow
            - Question-answer patterns

            Target question: {question}"""

        # Use enhanced chunk-type-based retrieval
        print(f"🔍 Using enhanced chunk-type-based retrieval (top {RAGConfig.TOP_K_PER_TYPE} from each type)...")
        enhanced_docs = self.retrieve_by_chunk_types(enhanced_query)

        # Update the debug handler with enhanced results
        self.debug_handler.retrieved_docs = enhanced_docs
        self.debug_handler.on_retriever_end(enhanced_docs)

        # Create context from enhanced docs with chunk type grouping
        context_parts = []
        current_chunk_type = None

        for doc in enhanced_docs:
            chunk_type = doc.metadata.get('chunk_type', 'unknown')
            if chunk_type != current_chunk_type:
                if current_chunk_type is not None:
                    context_parts.append("")  # Add separator between chunk types
                context_parts.append(f"=== {chunk_type.upper()} CHUNKS ===")
                current_chunk_type = chunk_type
            context_parts.append(doc.page_content)

        context = "\n\n".join(context_parts)

        # Get LLM response directly with error handling
        try:
            llm = ChatOpenAI(
                model=RAGConfig.DEFAULT_LLM_MODEL,
                temperature=RAGConfig.DEFAULT_TEMPERATURE,
                openai_api_key=self.openai_api_key
            )

            prompt = f"""You are analyzing a medical conversation with temporal and chunk-type context.

CONTEXT ORGANIZATION:
The context is organized by chunk types:
- DIALOGUE_TURNS: Natural conversation flow chunks
- BLIND_CHARACTER_SPLIT: Text-based chunks for comprehensive coverage
- QUESTION_ANSWER_FOCUSED: Chunks focused on specific Q&A patterns

TEMPORAL MARKERS: Pay attention to [CONVERSATION TIMING: ...] which show:
- EARLY/MIDDLE/LATE conversation timing
- Percentage through conversation
- Turn numbers and sequence

OASIS CODES: Look for [OASIS CODES: ...] which show relevant medical assessment codes

INSTRUCTIONS:
1. Analyze ALL chunk types to find the most relevant information
2. Use temporal context to understand progression and sequence
3. For memory tests, distinguish between initial instruction and later recall attempts
4. If multiple chunk types have relevant info, synthesize from the most comprehensive source
5. If you don't know the answer based on the context, say "Not Available"

Context (organized by chunk types with temporal and OASIS information):
{context}

Question: {enhanced_query}

Answer:"""

            self.debug_handler.on_llm_start({}, [prompt])

            response = llm.invoke(prompt)
            result_text = response.content

            self.debug_handler.llm_output = result_text
            self.debug_handler.on_llm_end(response)

            return {
                "answer": result_text,
                "source_documents": enhanced_docs,
                "retrieved_context": [doc.page_content for doc in enhanced_docs],
                "retrieved_chunks_detailed": [
                    {
                        "chunk_index": i+1,
                        "content": doc.page_content,
                        "metadata": doc.metadata if hasattr(doc, 'metadata') else {},
                        "question_codes": self._get_question_codes_from_metadata(doc.metadata) if hasattr(doc, 'metadata') else []
                    }
                    for i, doc in enumerate(enhanced_docs)
                ],
                "query": question,
                "enhanced_query": enhanced_query,
                "detected_question_codes": question_codes,
                "llm_input": prompt,
                "llm_output": result_text
            }

        except Exception as e:
            logger.error(f"Error during LLM query processing: {e}", exc_info=True)
            # Return error response
            return {
                "answer": "Error: Unable to process the query due to an internal error.",
                "source_documents": enhanced_docs,
                "retrieved_context": [doc.page_content for doc in enhanced_docs],
                "retrieved_chunks_detailed": [],
                "query": question,
                "enhanced_query": enhanced_query,
                "detected_question_codes": question_codes,
                "llm_input": prompt if 'prompt' in locals() else "",
                "llm_output": f"Error: {str(e)}",
                "error": str(e)
            }
    
    def cleanup(self):
        """Clean up resources"""
        if self.vectorstore:
            try:
                # Chroma cleanup if needed
                pass
            except Exception as e:
                logger.warning(f"Error during cleanup: {e}")


def get_llm_response_langchain(rag_system: LangChainRAG, user_content: str) -> dict:
    """
    Get LLM response using LangChain RAG system

    Args:
        rag_system: The initialized LangChain RAG system
        user_content: The user query/content

    Returns:
        Dictionary containing the LLM response and retrieval information
    """
    try:
        result = rag_system.query(user_content)
        return {
            "answer": result["answer"],
            "retrieval_info": {
                "retrieved_chunks": result["retrieved_chunks_detailed"],
                "query": result["query"],
                "llm_input": result["llm_input"],
                "llm_output": result["llm_output"]
            }
        }
    except Exception as e:
        logger.error(f"Error getting LLM response: {e}")
        raise e


def format_json_response_using_llm(raw_string: str):
    """
    Convert to structured JSON format from string using LLM
    Same as original implementation
    """
    try:
        response = openai.ChatCompletion.create(
            model="gpt-4-turbo",
            response_format={ "type": "json_object" },
            messages=[
                {
                    "role": "system",
                    "content": """
    You are a helpful assistant designed to output JSON from raw string with below format : 
    
    ### OUTPUT FORMAT :
    ```
    "response" : {
        [
            {
                "question_code": <question_code>,
                "question_text": <question_text>,
                "question_type": <question_type>,
                "answer_context": <answer_context>,
                "answer_text": <answer_text>,
                "answer_code": <answer_code>
            },
            {
                "question_code": <question_code>,
                "question_text": <question_text>,
                "question_type": <question_type>,
                "answer_context": <answer_context>,
                "answer_text": <answer_text>,
                "answer_code": <answer_code>
            }
        ]
    }
    ```
    """
                },
                {"role": "user", "content": raw_string}
            ]
        )
        
        json_data = json.loads(response.choices[0].message.content)
    
        if "response" in json_data.keys():
            return json_data.get("response")
            
        elif "result" in json_data.keys():
            return json_data.get("result")
            
        elif "question_data" in json_data.keys():
            return json_data.get("question_data")
            
        else:
            return json_data
        
    except Exception as e:
        raise e        


# Helper functions - same as original
def split_assessment_list(assessment_list, chunk_size):
    """Splits a list into chunks of a given size."""
    return [assessment_list[i:i + chunk_size] for i in range(0, len(assessment_list), chunk_size)]

def prepare_user_content(splitted_list, template):
    """Prepares user content by replacing placeholders in the template."""
    user_content_list = []
    for item in splitted_list:
        user_content_copy = template.replace("{{role_injection}}", INSTRUCTIONS)
        user_content_copy = user_content_copy.replace("{{question_list_in_json}}", json.dumps(item))
        user_content_copy = user_content_copy.replace("{{todays_date - 1 year}}", str(datetime.now().strftime("%m/%d/%Y"))[:10])
        user_content_copy = user_content_copy.replace("{{todays_date}}", str(datetime.now().strftime("%m/%d/%Y"))[:10])
        user_content_list.append(user_content_copy)
    return user_content_list

def save_human_readable_retrieval(retrieval_data_list, embedding_model, client_id, assessment_id):
    """
    Save retrieval chunks in human-readable format
    """
    model_name = embedding_model.replace("-", "_").replace(":", "_").replace("/", "_")
    readable_filename = f"retrieval_readable_{model_name}_{client_id}_{int(time.time())}.txt"

    with open(readable_filename, "w", encoding="utf-8") as file:
        file.write("=" * 80 + "\n")
        file.write("RETRIEVAL ANALYSIS REPORT\n")
        file.write("=" * 80 + "\n\n")

        file.write(f"📊 Embedding Model: {embedding_model}\n")
        file.write(f"👤 Client ID: {client_id}\n")
        file.write(f"📋 Assessment ID: {assessment_id}\n")
        file.write(f"📅 Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        file.write(f"🔍 Total Queries: {len(retrieval_data_list)}\n\n")

        for query_idx, retrieval_data in enumerate(retrieval_data_list, 1):
            retrieval_info = retrieval_data["retrieval_info"]

            file.write("=" * 80 + "\n")
            file.write(f"QUERY {query_idx}\n")
            file.write("=" * 80 + "\n\n")

            # Show what was asked
            file.write("🤔 QUESTIONS ASKED:\n")
            file.write("-" * 50 + "\n")
            query_text = retrieval_info["query"]
            # Extract just the questions part
            if "## Questions to Answer:" in query_text:
                questions_part = query_text.split("## Questions to Answer:")[1].strip()
                try:
                    import json
                    questions = json.loads(questions_part)
                    for i, q in enumerate(questions, 1):
                        file.write(f"{i}. {q.get('question', 'N/A')}\n\n")
                except:
                    file.write("Could not parse questions\n\n")

            # Show retrieved chunks in readable format
            file.write("📄 RETRIEVED CONVERSATION CHUNKS:\n")
            file.write("-" * 50 + "\n")

            for chunk in retrieval_info["retrieved_chunks"]:
                chunk_metadata = chunk.get('metadata', {})
                chunk_type = chunk_metadata.get('chunk_type', 'unknown')

                # Get question codes assigned by LLM
                question_codes = chunk.get('question_codes', [])
                if not question_codes:
                    # Try to extract from metadata using our helper method format
                    original_codes_str = chunk_metadata.get('_original_question_codes', '')
                    if original_codes_str:
                        question_codes = [code.strip() for code in original_codes_str.split(',') if code.strip()]
                    else:
                        # Fallback to regular question_codes string format
                        question_codes_str = chunk_metadata.get('question_codes', '')
                        question_codes = [code.strip() for code in question_codes_str.split(',') if code.strip()]

                # Get temporal information
                conversation_order = chunk_metadata.get('conversation_order', 'N/A')
                temporal_position = chunk_metadata.get('temporal_position', 'N/A')
                relative_timing = chunk_metadata.get('relative_timing', 'unknown')
                start_turn = chunk_metadata.get('start_turn_index', 'N/A')
                end_turn = chunk_metadata.get('end_turn_index', 'N/A')

                file.write(f"\n📋 Chunk {chunk['chunk_index']} ({chunk_type}) - 🕐 Order: {conversation_order}:\n")

                # Add temporal information
                file.write(f"⏰ Timing: {temporal_position}% through conversation ({relative_timing})\n")
                if start_turn != 'N/A' and end_turn != 'N/A':
                    file.write(f"📍 Conversation Turns: {start_turn}-{end_turn}\n")

                # Add question codes information
                if question_codes:
                    file.write(f"🏷️  OASIS Question Codes: {', '.join(question_codes)}\n")
                    file.write(f"📊 Total Codes Assigned: {len(question_codes)}\n")
                else:
                    file.write(f"🏷️  OASIS Question Codes: None assigned\n")

                file.write("." * 40 + "\n")

                # Parse and format the conversation content based on chunk type
                content = chunk["content"]
                try:
                    # Check if this is a semantic chunk (already formatted)
                    if 'CLINICIAN:' in content or 'PATIENT:' in content:
                        # Semantic chunks are already formatted with CLINICIAN/PATIENT labels
                        lines = content.split('\n')
                        for line in lines:
                            line = line.strip()
                            if line:
                                if line.startswith('CLINICIAN:'):
                                    file.write(f"🩺 {line}\n")
                                elif line.startswith('PATIENT:'):
                                    file.write(f"👤 {line}\n")
                                else:
                                    file.write(f"💬 {line}\n")
                    else:
                        # Blind chunks need JSON parsing
                        lines = content.split('\n')
                        for line in lines:
                            line = line.strip()
                            if line and '"text":' in line:
                                # Extract the text content
                                text_start = line.find('"text": "') + 9
                                text_end = line.rfind('"')
                                if text_start > 8 and text_end > text_start:
                                    text = line[text_start:text_end]
                                    file.write(f"💬 {text}\n")
                            elif line and '"speaker_id":' in line:
                                # Extract speaker info
                                if '"speaker_id": 0' in line:
                                    file.write("\n🩺 CLINICIAN:\n")
                                elif '"speaker_id": 1' in line:
                                    file.write("\n👤 PATIENT:\n")

                        # If no JSON structure found, show raw content (truncated)
                        if not any('"text":' in line for line in lines):
                            file.write(content[:500] + "...\n" if len(content) > 500 else content + "\n")

                except:
                    # Fallback to raw content if parsing fails
                    file.write(content[:500] + "...\n" if len(content) > 500 else content + "\n")

                file.write("." * 40 + "\n")

            # Show LLM response
            file.write(f"\n🤖 LLM RESPONSE:\n")
            file.write("-" * 50 + "\n")
            file.write(retrieval_info["llm_output"] + "\n\n")

    print(f"📖 Human-readable retrieval saved to: {readable_filename}")
    return readable_filename


def save_chunk_analysis_report(vectorstore, embedding_model, client_id, assessment_id, questions):
    """
    Save a comprehensive analysis report of all chunks with their question codes

    Args:
        vectorstore: The Chroma vector store containing all chunks
        embedding_model: The embedding model used
        client_id: Client identifier
        assessment_id: Assessment identifier
        questions: List of OASIS questions for reference
    """
    model_name = embedding_model.replace("-", "_").replace(":", "_").replace("/", "_")
    analysis_filename = f"chunk_analysis_{model_name}_{client_id}_{int(time.time())}.txt"

    # Create a mapping of question codes to question details for reference
    question_map = {}
    for q in questions:
        code = q.get('question_code', '')
        if code:
            question_map[code] = {
                'question': q.get('question', ''),
                'labelName': q.get('labelName', ''),
                'section': q.get('section', '')
            }

    try:
        # Get all documents from the vector store
        all_data = vectorstore.get()
        documents = all_data.get('documents', [])
        metadatas = all_data.get('metadatas', [])

        with open(analysis_filename, "w", encoding="utf-8") as file:
            file.write("=" * 80 + "\n")
            file.write("COMPREHENSIVE CHUNK ANALYSIS REPORT\n")
            file.write("=" * 80 + "\n\n")

            file.write(f"📊 Embedding Model: {embedding_model}\n")
            file.write(f"👤 Client ID: {client_id}\n")
            file.write(f"📋 Assessment ID: {assessment_id}\n")
            file.write(f"📅 Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            file.write(f"📄 Total Chunks: {len(documents)}\n")
            file.write(f"❓ Total OASIS Questions: {len(questions)}\n\n")

            # Calculate statistics
            def get_question_codes_from_metadata(metadata):
                if not metadata:
                    return []
                # Try original codes first
                original_codes_str = metadata.get('_original_question_codes', '')
                if original_codes_str:
                    return [code.strip() for code in original_codes_str.split(',') if code.strip()]
                # Fallback to regular format
                question_codes_str = metadata.get('question_codes', '')
                if question_codes_str:
                    return [code.strip() for code in question_codes_str.split(',') if code.strip()]
                return []

            tagged_chunks = sum(1 for m in metadatas if m and get_question_codes_from_metadata(m))
            total_tags = sum(len(get_question_codes_from_metadata(m)) for m in metadatas if m)

            file.write("📈 TAGGING STATISTICS:\n")
            file.write("-" * 50 + "\n")
            file.write(f"🏷️  Tagged Chunks: {tagged_chunks}/{len(documents)} ({tagged_chunks/len(documents)*100:.1f}%)\n")
            file.write(f"🔢 Total Question Code Assignments: {total_tags}\n")
            file.write(f"📊 Average Tags per Chunk: {total_tags/len(documents):.1f}\n")
            file.write(f"📊 Average Tags per Tagged Chunk: {total_tags/tagged_chunks:.1f}\n\n" if tagged_chunks > 0 else "\n")

            # Show question code distribution
            code_counts = {}
            for metadata in metadatas:
                if metadata:
                    codes = get_question_codes_from_metadata(metadata)
                    for code in codes:
                        code_counts[code] = code_counts.get(code, 0) + 1

            file.write("📋 QUESTION CODE DISTRIBUTION:\n")
            file.write("-" * 50 + "\n")
            for code, count in sorted(code_counts.items()):
                question_info = question_map.get(code, {})
                label = question_info.get('labelName', 'Unknown')
                section = question_info.get('section', 'Unknown')
                file.write(f"{code} ({label} - {section}): {count} chunks\n")
            file.write("\n")

            # Show chunk type distribution
            chunk_type_counts = {}
            for metadata in metadatas:
                if metadata:
                    chunk_type = metadata.get('chunk_type', 'unknown')
                    chunk_type_counts[chunk_type] = chunk_type_counts.get(chunk_type, 0) + 1

            file.write("📊 CHUNK TYPE DISTRIBUTION:\n")
            file.write("-" * 50 + "\n")
            for chunk_type, count in sorted(chunk_type_counts.items()):
                file.write(f"{chunk_type}: {count} chunks\n")
            file.write("\n")

            # Show detailed chunk analysis
            file.write("📄 DETAILED CHUNK ANALYSIS:\n")
            file.write("=" * 80 + "\n\n")

            for i, (doc_content, metadata) in enumerate(zip(documents, metadatas), 1):
                if not metadata:
                    continue

                chunk_type = metadata.get('chunk_type', 'unknown')
                question_codes = get_question_codes_from_metadata(metadata)
                turn_count = metadata.get('turn_count', 'N/A')

                # Get temporal information
                conversation_order = metadata.get('conversation_order', 'N/A')
                temporal_position = metadata.get('temporal_position', 'N/A')
                relative_timing = metadata.get('relative_timing', 'unknown')
                start_turn = metadata.get('start_turn_index', 'N/A')
                end_turn = metadata.get('end_turn_index', 'N/A')

                file.write(f"📋 CHUNK {i} ({chunk_type}) - 🕐 Order: {conversation_order}:\n")
                file.write("-" * 60 + "\n")

                # Chunk metadata
                file.write(f"🔢 Turn Count: {turn_count}\n")
                file.write(f"📏 Content Length: {len(doc_content)} characters\n")

                # Temporal information
                file.write(f"⏰ Temporal Position: {temporal_position}% through conversation ({relative_timing})\n")
                if start_turn != 'N/A' and end_turn != 'N/A':
                    file.write(f"📍 Conversation Turns: {start_turn}-{end_turn}\n")

                # Question codes with details
                if question_codes:
                    file.write(f"🏷️  OASIS Question Codes ({len(question_codes)}):\n")
                    for code in question_codes:
                        question_info = question_map.get(code, {})
                        question_text = question_info.get('question', 'Unknown question')
                        label = question_info.get('labelName', 'Unknown')
                        section = question_info.get('section', 'Unknown')
                        file.write(f"   • {code}: {label}\n")
                        file.write(f"     Section: {section}\n")
                        file.write(f"     Question: {question_text[:100]}{'...' if len(question_text) > 100 else ''}\n")
                else:
                    file.write(f"🏷️  OASIS Question Codes: None assigned\n")

                file.write("\n💬 CONTENT PREVIEW:\n")

                # Format content preview
                if 'CLINICIAN:' in doc_content or 'PATIENT:' in doc_content:
                    # Semantic chunks are already formatted
                    lines = doc_content.split('\n')[:5]  # Show first 5 lines
                    for line in lines:
                        line = line.strip()
                        if line:
                            if line.startswith('CLINICIAN:'):
                                file.write(f"🩺 {line}\n")
                            elif line.startswith('PATIENT:'):
                                file.write(f"👤 {line}\n")
                            else:
                                file.write(f"💬 {line}\n")
                    if len(doc_content.split('\n')) > 5:
                        file.write("   ... (content truncated)\n")
                else:
                    # Show raw content preview
                    preview = doc_content[:300] + "..." if len(doc_content) > 300 else doc_content
                    file.write(f"💬 {preview}\n")

                file.write("\n" + "=" * 80 + "\n\n")

        print(f"📊 Comprehensive chunk analysis saved to: {analysis_filename}")
        return analysis_filename

    except Exception as e:
        print(f"❌ Error creating chunk analysis report: {e}")
        return None


def pre_process_questions(questions:list):
    for question in questions:
        # remove the answer_context from each question if it exists
        if "answer_context" in question:
            question.pop("answer_context")

        # Check if the question has options and options is an array
        if "options" in question and isinstance(question["options"], list):
            # Add "Not Available" to options if it's not already in the list
            if "Not Available" not in question["options"]:
                question["options"].append("Not Available")

    return questions

def tag_chunks_with_question_codes(chunks: List[Document], questions: list, openai_api_key: str = None) -> List[Document]:
    """
    Tag conversation chunks with OASIS medical question codes using LLM

    Args:
        chunks: List of LangChain Document objects containing conversation chunks
        questions: List of OASIS questions with question codes
        openai_api_key: OpenAI API key for LLM calls

    Returns:
        List of Document objects with question_codes added to metadata
    """
    print(f"🏷️  Tagging {len(chunks)} chunks with OASIS question codes...")

    tagged_chunks = []

    # Create a simplified question list for the LLM prompt
    simplified_questions = []
    for q in questions:
        simplified_questions.append({
            "question_code": q.get("question_code", ""),
            "question": q.get("question", ""),
            "labelName": q.get("labelName", ""),
            "section": q.get("section", "")
        })

    for i, chunk in enumerate(chunks):
        prompt = f"""You are an expert clinical assistant specializing in OASIS (Outcome and Assessment Information Set) medical assessments.

TASK: Analyze the conversation chunk below and identify which OASIS question codes this chunk could potentially help answer.

CONVERSATION CHUNK:
---
{chunk.page_content}
---

OASIS QUESTIONS TO CONSIDER:
{json.dumps(simplified_questions, indent=2)}

INSTRUCTIONS:
1. Read the conversation chunk carefully
2. For each OASIS question, determine if the chunk contains information that could help answer that question
3. Consider both direct answers and relevant context (e.g., symptoms, conditions, abilities, medications, etc.)
4. Be inclusive - if there's any relevant information, include the question code
5. Return ONLY a JSON array of question codes

RESPONSE FORMAT:
Return only a JSON array of question codes. Example: ["M1800", "M1028", "M1242"]
If no questions can be answered by this chunk, return: []
"""

        try:
            # Use the newer OpenAI client format
            import openai
            client = openai.OpenAI(api_key=openai_api_key) if openai_api_key else openai.OpenAI()

            response = client.chat.completions.create(
                model=RAGConfig.TAGGING_LLM_MODEL,
                messages=[{"role": "user", "content": prompt}],
                temperature=RAGConfig.TAGGING_TEMPERATURE,
                max_tokens=RAGConfig.MAX_TAGGING_TOKENS
            )

            response_text = response.choices[0].message.content.strip()

            # Clean up the response to extract just the JSON array
            if response_text.startswith("```json"):
                response_text = response_text.replace("```json", "").replace("```", "").strip()
            elif response_text.startswith("```"):
                response_text = response_text.replace("```", "").strip()

            # Parse the question codes
            try:
                question_codes = json.loads(response_text)
                if not isinstance(question_codes, list):
                    question_codes = []
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse LLM response as JSON for chunk {i+1}: {response_text}")
                question_codes = []

            # Add question codes to chunk metadata
            # Convert list to string for Chroma compatibility (Chroma doesn't support list metadata)
            chunk.metadata["question_codes"] = ",".join(question_codes) if question_codes else ""
            chunk.metadata["question_codes_list"] = question_codes  # Keep original list for internal use
            chunk.metadata["tagged"] = True

            # Add question codes to chunk content for LLM visibility
            if question_codes:
                question_codes_header = f"[OASIS CODES: {', '.join(question_codes)}]"
                chunk.page_content = question_codes_header + "\n" + chunk.page_content
                print(f"   Chunk {i+1} ({chunk.metadata.get('chunk_type', 'unknown')}): Tagged with {len(question_codes)} question codes")

            tagged_chunks.append(chunk)

        except openai.RateLimitError as e:
            logger.warning(f"Rate limit exceeded for chunk {i+1}: {e}")
            # Add empty question codes on rate limit
            chunk.metadata["question_codes"] = ""
            chunk.metadata["question_codes_list"] = []
            chunk.metadata["tagged"] = False
            tagged_chunks.append(chunk)
        except openai.APIError as e:
            logger.error(f"OpenAI API error for chunk {i+1}: {e}")
            # Add empty question codes on API error
            chunk.metadata["question_codes"] = ""
            chunk.metadata["question_codes_list"] = []
            chunk.metadata["tagged"] = False
            tagged_chunks.append(chunk)
        except Exception as e:
            logger.error(f"Unexpected error tagging chunk {i+1}: {e}", exc_info=True)
            # Add empty question codes on failure
            chunk.metadata["question_codes"] = ""
            chunk.metadata["question_codes_list"] = []
            chunk.metadata["tagged"] = False
            tagged_chunks.append(chunk)

    # Print summary
    def get_question_codes_count(chunk):
        # Get question codes count - try list first, then string format
        codes = chunk.metadata.get('question_codes_list', [])
        if not codes:
            codes_str = chunk.metadata.get('question_codes', '')
            codes = [code.strip() for code in codes_str.split(',') if code.strip()]
        return len(codes)

    total_tags = sum(get_question_codes_count(chunk) for chunk in tagged_chunks)
    successfully_tagged = sum(1 for chunk in tagged_chunks if chunk.metadata.get("tagged", False))

    print(f"🏷️  Tagging complete: {successfully_tagged}/{len(chunks)} chunks tagged successfully")
    print(f"🏷️  Total question code assignments: {total_tags}")

    return tagged_chunks


def tag_existing_vectorstore_chunks(vectorstore, questions: list, openai_api_key: str = None) -> bool:
    """
    Tag existing chunks in a vector store with OASIS question codes

    Args:
        vectorstore: Existing Chroma vector store
        questions: List of OASIS questions with question codes
        openai_api_key: OpenAI API key for LLM calls

    Returns:
        Boolean indicating success
    """
    try:
        print(f"🏷️  Tagging existing vector store chunks with OASIS question codes...")

        # Get all documents from the vector store
        all_data = vectorstore.get()
        documents = all_data.get('documents', [])
        metadatas = all_data.get('metadatas', [])
        ids = all_data.get('ids', [])

        if not documents:
            print("⚠️  No documents found in vector store")
            return False

        print(f"📄 Found {len(documents)} chunks to tag")

        # Create Document objects for tagging
        doc_objects = []
        for doc_content, metadata in zip(documents, metadatas):
            doc_objects.append(Document(
                page_content=doc_content,
                metadata=metadata or {}
            ))

        # Tag the chunks
        tagged_docs = tag_chunks_with_question_codes(doc_objects, questions, openai_api_key)

        # Update the vector store with new metadata
        updated_metadatas = []
        for doc in tagged_docs:
            updated_metadatas.append(doc.metadata)

        # Update the vector store (this requires recreating it with updated metadata)
        # Note: Chroma doesn't support direct metadata updates, so we need to delete and re-add

        # Delete existing documents
        vectorstore.delete(ids=ids)

        # Re-add documents with updated metadata
        vectorstore.add_texts(
            texts=documents,
            metadatas=updated_metadatas,
            ids=ids
        )

        print(f"✅ Successfully tagged {len(tagged_docs)} chunks in vector store")
        return True

    except Exception as e:
        print(f"❌ Error tagging existing vector store chunks: {e}")
        return False


def create_enhanced_retriever(vectorstore, search_type: str = "mmr", k: int = 5,
                            lambda_mult: float = 0.5, fetch_k: int = 24):
    """
    Create an enhanced retriever that can use question code metadata for better retrieval

    Args:
        vectorstore: The vector store to create retriever from
        search_type: Type of search ("mmr", "similarity", "similarity_score_threshold")
        k: Number of documents to retrieve
        lambda_mult: Lambda multiplier for MMR (diversity vs relevance)
        fetch_k: Number of documents to fetch before MMR filtering

    Returns:
        Enhanced retriever object
    """
    retriever = vectorstore.as_retriever(
        search_type=search_type,
        search_kwargs={
            "k": k,
            "lambda_mult": lambda_mult,
            "fetch_k": fetch_k
        }
    )

    # Add custom filtering method
    def filter_by_question_codes(docs: List[Document], question_codes: List[str]) -> List[Document]:
        """Filter documents by question codes"""
        if not question_codes:
            return docs

        # Score documents based on question code matches
        scored_docs = []
        for doc in docs:
            # Get question codes using helper method (handles all formats)
            doc_codes = []
            if hasattr(doc, 'metadata') and doc.metadata:
                # Try original codes first (stored as string)
                original_codes_str = doc.metadata.get('_original_question_codes', '')
                if original_codes_str:
                    doc_codes = [code.strip() for code in original_codes_str.split(',') if code.strip()]
                else:
                    # Fallback to regular question_codes string format
                    question_codes_str = doc.metadata.get('question_codes', '')
                    doc_codes = [code.strip() for code in question_codes_str.split(',') if code.strip()]

            match_score = len(set(doc_codes) & set(question_codes))
            scored_docs.append((doc, match_score))

        # Sort by match score (descending) and return documents
        scored_docs.sort(key=lambda x: x[1], reverse=True)
        return [doc for doc, _ in scored_docs]

    # Attach the filtering method to the retriever
    retriever.filter_by_question_codes = filter_by_question_codes

    return retriever
def answer_questions(conversation_data: dict, questions: list, client_id: str, assessment_id: str,
                    embedding_model: str = "text-embedding-ada-002"):
    """
    Main function to answer questions using LangChain RAG

    Args:
        conversation_data: The conversation data
        questions: List of questions to answer
        client_id: Client identifier
        assessment_id: Assessment identifier
        embedding_model: Embedding model to use (default: OpenAI's text-embedding-ada-002)
    """
    # Initialize settings first - specify the correct .env file path
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    env_file_path = os.path.join(current_dir, "..", "..", ".env")

    settings, _ = create_app_settings("ai_core", AiCoreConfig, env_file=env_file_path)
    openai.api_key = settings.openai_key

    if not settings.openai_key:
        print("ERROR: OpenAI key not found in settings!")
        return None

    questions = pre_process_questions(questions)

    rag_system = None

    try:
        # Initialize LangChain RAG system
        rag_system = LangChainRAG(
            embedding_model=embedding_model,
            openai_api_key=settings.openai_key
        )

        # Create vector store from conversation data with question tagging
        rag_system.create_vectorstore_from_conversation(conversation_data, client_id, questions)

        # Setup QA chain
        rag_system.setup_qa_chain(
            llm_model=settings.openai_model,
            temperature=0.0
        )

        # Process questions in chunks
        chunk_size = number_of_question
        assessment_splitted_list = split_assessment_list(questions, chunk_size)
        user_content_list = prepare_user_content(assessment_splitted_list, user_content_default)

        question_answer_list = []
        retrieval_data_list = []

        # Process each chunk
        with ThreadPoolExecutor() as executor:
            futures = []
            for user_content in user_content_list:
                future = executor.submit(get_llm_response_langchain, rag_system, user_content)
                futures.append(future)

            for future in futures:
                try:
                    result = future.result()
                    response = result["answer"]
                    retrieval_info = result["retrieval_info"]

                    # Store retrieval information
                    retrieval_data_list.append({
                        "timestamp": time.time(),
                        "embedding_model": embedding_model,
                        "client_id": client_id,
                        "assessment_id": assessment_id,
                        "retrieval_info": retrieval_info
                    })

                    if response:
                        string = response.replace("```", "").replace("json", "").strip()
                        try:
                            json_data = json.loads(string)
                            logger.info(f"type of json_data: {type(json_data)}")

                            if isinstance(json_data, list):
                                jsonschema.validate(instance=json_data, schema=response_schema)
                                for item in json_data:
                                    question_answer_list.append(item)
                            else:
                                jsonschema.validate(instance=json_data, schema=single_response_schema)
                                question_answer_list.append(json_data)

                        except Exception as ve:
                            logger.error(f"JSON does not match expected schema: {ve}")
                            # send_slack_message(f"JSON does not match expected schema: {ve}")

                except Exception as e:
                    logger.error(f"Error processing future: {e}")

        # Success message (commented out for clean output)
        # slack_message = (f"Whole process completed successfully with Question-Answer pair (LangChain).\n\n"
        # f"Date : *{time.ctime(time.time())}*\n"
        # f"Client ID : *{client_id}*\n"
        # f"Assessment ID : *{assessment_id}*\n"
        # f"Embedding Model : *{embedding_model}*")
        # send_slack_message(slack_message)

        # Save retrieval data to separate file (commented for deployment)
        # model_name = embedding_model.replace("-", "_").replace(":", "_").replace("/", "_")
        # retrieval_filename = f"retrieval_chunks_{model_name}_{client_id}_{int(time.time())}.json"

        # retrieval_summary = {
        #     "embedding_model": embedding_model,
        #     "client_id": client_id,
        #     "assessment_id": assessment_id,
        #     "timestamp": time.time(),
        #     "total_queries": len(retrieval_data_list),
        #     "retrieval_data": retrieval_data_list
        # }

        # File generation commented out for deployment
        # with open(retrieval_filename, "w", encoding="utf-8") as file:
        #     json.dump(retrieval_summary, file, indent=2)

        # print(f"📄 Retrieval chunks saved to: {retrieval_filename}")

        # Also save in human-readable format (commented for deployment)
        # readable_filename = save_human_readable_retrieval(
        #     retrieval_data_list, embedding_model, client_id, assessment_id
        # )

        # Save comprehensive chunk analysis report (commented for deployment)
        # chunk_analysis_filename = save_chunk_analysis_report(
        #     rag_system.vectorstore, embedding_model, client_id, assessment_id, questions
        # )

        final_data_dict = {
            "ClientID": client_id,
            "Responses": question_answer_list,
            # File references commented out for deployment
            # "RetrievalDataFile": retrieval_filename,
            # "ReadableRetrievalFile": readable_filename,
            # "ChunkAnalysisFile": chunk_analysis_filename
        }

        return final_data_dict

    except Exception as e:
        error_message = (
            f"Something went wrong while generating question-answer file (LangChain).\n\n"
            f"Date : *{time.ctime(time.time())}*\n"
            f"Client ID : *{client_id}*\n"
            f"Assessment ID : *{assessment_id}*\n"
            f"Embedding Model : *{embedding_model}*\n"
            f"Error : *{str(e)}*\n"
            f"Line no. : *{e.__traceback__.tb_lineno}*"
        )
        logger.error(error_message)
        # send_slack_message(error_message)
        return None

    finally:
        # Cleanup
        if rag_system:
            rag_system.cleanup()


def main():
    """
    Main function for testing - same structure as original but with LangChain
    """
    # Configure logging to only show errors
    logging.basicConfig(
        level=logging.ERROR,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()  # This outputs to console
        ]
    )

    try:
        # Initialize settings first - specify the correct .env file path
        from config import create_app_settings
        import os

        # Get the correct path to .env file (go up from src directory)
        current_dir = os.path.dirname(os.path.abspath(__file__))
        env_file_path = os.path.join(current_dir, "..", "..", ".env")

        settings, _ = create_app_settings("ai_core", AiCoreConfig, env_file=env_file_path)

        # Check if OpenAI key is loaded
        if not settings.openai_key:
            print("ERROR: OpenAI key not found in settings!")
            return

        with open(os.path.join("tests","data","convo1.json"), "r", encoding="utf-8") as file:
            conversation_data = json.load(file)

        with open(os.path.join("tests","data","questionsForm.json"), "r", encoding="utf-8") as file:
            questions = json.load(file)

        # 🔄 EASY MODEL SWITCHING - Uncomment the models you want to test
        embedding_models = [
            # === OpenAI Models ===
            # "text-embedding-ada-002",        # OpenAI default - fast and cheap
            # "text-embedding-3-small",      # OpenAI newer - better performance
            # "text-embedding-3-large",      # OpenAI best - highest quality

            # === Medical/Clinical Models ===
            # "michiyasunaga/BioLinkBERT-large",  # BioLinkBERT - biomedical specialist
            "emilyalsentzer/Bio_ClinicalBERT",  # BioClinical BERT - medical specialist
            # "microsoft/BiomedNLP-PubMedBERT-base-uncased-abstract-fulltext",  # PubMed BERT

            # === General Purpose Models ===
            # "sentence-transformers/all-MiniLM-L6-v2",     # Fast general purpose
            # "sentence-transformers/all-mpnet-base-v2",    # High quality general purpose
        ]

        print("🚀 Starting LangChain RAG processing...")

        for embedding_model in embedding_models:
            print(f"\n📊 Using embedding model: {embedding_model}")
            answers = answer_questions(
                conversation_data,
                questions,
                client_id="kate",
                assessment_id="assessment-1",
                embedding_model=embedding_model
            )

            # Save results with embedding model in filename
            model_name = embedding_model.replace("-", "_").replace(":", "_").replace("/", "_")
            filename = f"answers_langchain_{model_name}_{time.time()}.json"
            with open(os.path.join(filename), "w", encoding="utf-8") as file:
                json.dump(answers, file, indent=2)

            print(f"✅ Results saved to: {filename}")

    except Exception as e:
        print(f"Error in main: {e}")
        logger.error(f"Error in main: {e}", exc_info=True)


if __name__ == "__main__":
    main()
