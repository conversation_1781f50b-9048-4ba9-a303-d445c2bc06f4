{"clinician_observation_config": {"Functional Abilities": {"enabled": true, "search_queries": ["does the patient use wheelchair or walker or cane?"], "observation_prompt": "Based on the conversation context, identify any mobility aids (wheelchair, walker, cane) or assistive devices the patient uses. example, if you detect patient uses any mobility aid,"}, "Cognitive Status": {"enabled": true, "search_queries": ["confusion OR disoriented OR memory OR forgetful OR cognitive", "alert OR oriented OR awareness OR mental status", "dementia OR <PERSON><PERSON><PERSON> OR cognitive impairment"], "observation_prompt": "Based on the conversation context, assess the patient's cognitive status including orientation, memory, awareness, and any signs of confusion or cognitive impairment observed during the conversation."}, "ADLs Assessment": {"enabled": true, "search_queries": ["bathing OR shower OR hygiene OR grooming OR dressing", "feeding OR eating OR meal preparation OR cooking", "toileting OR bathroom OR incontinence OR bladder OR bowel"], "observation_prompt": "Based on the conversation context, identify the patient's independence level with activities of daily living including bathing, dressing, feeding, toileting, and personal hygiene. Note any assistance needed or adaptive equipment used."}, "Mood/Behavior": {"enabled": true, "search_queries": ["depression OR sad OR mood OR anxiety OR worried", "behavior OR agitation OR aggressive OR withdrawn", "social OR isolation OR family OR support"], "observation_prompt": "Based on the conversation context, assess the patient's mood, emotional state, behavioral patterns, and social interactions observed during the conversation. Note any signs of depression, anxiety, or behavioral concerns."}, "Pain Assessment": {"enabled": true, "search_queries": ["pain OR hurt OR ache OR discomfort OR sore", "medication OR pain relief OR analgesic", "chronic pain OR acute pain OR pain management"], "observation_prompt": "Based on the conversation context, identify any pain complaints, pain levels, pain management strategies, and how pain affects the patient's daily activities as observed during the conversation."}, "Skin/Wound Assessment": {"enabled": true, "search_queries": ["wound OR ulcer OR sore OR pressure sore OR bedsore", "skin OR rash OR bruise OR cut OR injury", "dressing OR bandage OR wound care OR healing"], "observation_prompt": "Based on the conversation context, identify any skin conditions, wounds, pressure ulcers, or skin integrity issues mentioned or observed during the conversation. Note wound care needs and healing status."}, "Medications": {"enabled": true, "search_queries": ["medication OR medicine OR drug OR prescription OR pill", "compliance OR adherence OR missed doses OR side effects", "pharmacy OR refill OR dosage OR timing"], "observation_prompt": "Based on the conversation context, assess the patient's medication management, compliance, understanding of medications, and any medication-related issues observed during the conversation."}, "Vision & Communication": {"enabled": true, "search_queries": ["vision OR sight OR blind OR glasses OR see OR visual", "hearing OR deaf OR hearing aid OR communication", "speech OR language OR understand OR comprehension"], "observation_prompt": "Based on the conversation context, assess the patient's vision, hearing, speech, and communication abilities observed during the conversation. Note any sensory impairments or communication barriers."}, "Respiratory Status": {"enabled": true, "search_queries": ["breathing OR shortness of breath OR oxygen OR respiratory", "cough OR wheeze OR lung OR pulmonary", "COPD OR asthma OR pneumonia OR respiratory condition"], "observation_prompt": "Based on the conversation context, assess the patient's respiratory status, breathing patterns, oxygen needs, and any respiratory symptoms observed during the conversation."}, "Elimination Status": {"enabled": true, "search_queries": ["bowel OR constipation OR diarrhea OR stool OR BM", "bladder OR urination OR incontinence OR catheter OR UTI", "toilet OR bathroom OR elimination OR continence"], "observation_prompt": "Based on the conversation context, assess the patient's bowel and bladder function, continence status, and any elimination issues observed during the conversation."}}}