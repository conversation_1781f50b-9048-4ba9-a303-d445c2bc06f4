from pydantic import BaseModel, <PERSON>
from typing import List, Any, Optional, Literal
import time
import json
import yaml

class TranscriptionRating(BaseModel):
    id: Optional[str] = Field(None, example="123e4567-e89b-12d3-a456-426614174000")
    request_id: Optional[str] = Field(None, example="123e4567-e89b-12d3-a456-426614174000")
    question_code: Optional[str] = Field(None, example="M0090")
    answer_code: Optional[str] = Field(None, example="correct, incorrect, hallucination")
    comment: Optional[str] = Field(None, example="lgtm")
    admin_id: Optional[str] = Field(None, example="<EMAIL>")

    @classmethod
    def from_yaml(cls, yaml_str: str):
        """Load TranscriptionRating from a YAML string."""
        data = yaml.safe_load(yaml_str)
        return cls(**data)

    @classmethod
    def from_yaml_file(cls, file_path: str):
        """Load TranscriptionRating from a YAML file."""
        with open(file_path, "r") as file:
            return cls.from_yaml(file.read())

    @classmethod
    def from_json(cls, json_str: str):
        """Load TranscriptionRating from a JSON string."""
        data = json.loads(json_str)
        return cls(**data)

    @classmethod
    def from_json_file(cls, file_path: str):
        """Load TranscriptionRating from a JSON file."""
        with open(file_path, "r") as file:
            return cls.from_json(file.read())