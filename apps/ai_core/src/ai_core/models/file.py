from pydantic import BaseModel, Field
from typing import List, Any, Optional, Literal
import time
import json
import yaml

class File(BaseModel):
    id: Optional[str] = Field(None, example="123e4567-e89b-12d3-a456-426614174000")
    path: Optional[str] = Field(None, example="data/file.txt")
    data: Optional[Any] = Field(None, example="Hello World")

    @classmethod
    def from_yaml(cls, yaml_str: str):
        """Load File from a YAML string."""
        data = yaml.safe_load(yaml_str)
        return cls(**data)

    @classmethod
    def from_yaml_file(cls, file_path: str):
        """Load File from a YAML file."""
        with open(file_path, "r") as file:
            return cls.from_yaml(file.read())

    @classmethod
    def from_json(cls, json_str: str):
        """Load File from a JSON string."""
        data = json.loads(json_str)
        return cls(**data)

    @classmethod
    def from_json_file(cls, file_path: str):
        """Load File from a JSON file."""
        with open(file_path, "r") as file:
            return cls.from_json(file.read())