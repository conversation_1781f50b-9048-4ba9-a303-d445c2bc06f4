import logging
import openai
from ai_core.slack import send_slack_message
from itertools import zip_longest
from concurrent.futures import ThreadPoolExecutor
import json
import jsonschema
import tempfile
from datetime import datetime
import io
import os
import time
import uuid
from config import get_settings
from ai_core.models.config import Config as AiCoreConfig


# FIXME - put in config
delete_openai_objects = False
number_of_question = 5

logger = logging.getLogger(__name__)


# Constants
INSTRUCTIONS = """
You are an expert system designed to extract relevant information/answer from conversations between clinician and patient.
Given a set of questions from the user, you will analyze the transcript and provide concise answers to the user's questions based on the information present in the conversation.
If the question is not found in the conversation, the default answer will be "Not Available"
"""

user_content_default = """
### Objective
Extract answers from the conversation transcript for each question listed below. Base your responses ONLY on information present in the conversation. Use ["Not Available"] for any question without a clear answer in the transcript.

Today's Date: {{todays_date}}

### Instructions:
1. Extract information ONLY from the provided conversation.
2. For each question, provide the exact relevant information without adding assumptions.
3. Use the format specified in the Output section.
4. Return ["Not Available"] when information cannot be found.
5. Select appropriate options from the provided choices when applicable.

### Input Format
Each question has this format:
- question_code: Unique identifier
- question: The text prompt
- question_type: Format type (radio-group, select-drop-down, checklist, checkbox, date-field, multiline-text-field)
- labelName: Display label
- section: Category
- options: Available choices (when applicable)

### Response Format Rules
- radio-group, select-drop-down, checkbox, date-field, multiline-text-field: Single string in answer_text array
- checklist: Can have multiple strings in answer_text array when multiple options apply
- All responses must include question_code, question_text, answer_context, and answer_text

### Output Schema (JSON only)
{
  "question_code": "[ID from input]",
  "question_type": "[Question Type for RPA]",
  "question_code": "[ID from input]",
  "answer_context": ["[Patient's exact words from transcript]"],
  "answer_text": ["[Selected option or extracted answer]"]
}

### Example
For a radio-group question about feeding ability where the transcript shows "I can feed myself":
{
  "question_code": "M1870",
  "question_type": "radio-group",
  "question_text": "Current ability to feed self meals and snacks safely",
  "answer_context": ["I can feed myself."],
  "answer_text": ["0 - Able to independently feed self."]
}

## Questions to Answer:
{{question_list_in_json}}
"""

response_schema = {
    "type": "array",
    "items": {
        "type": "object",
        "required": ["question_code", "question_text", "question_type", "answer_context", "answer_text"],
        "properties": {
            "question_code": {"type": "string"},
            "question_text": {"type": "string"},
            "question_type": {"type": "string"},
            "answer_context": {
                "type": "array",
                "items": {"type": "string"}
            },
            "answer_text": {
                "type": "array",
                "items": {"type": "string"}
            }
        },
        "additionalProperties": False
    }
}

single_response_schema = {
    "type": "object",
    "required": ["question_code", "question_text", "question_type", "answer_context", "answer_text"],
    "properties": {
        "question_code": {"type": "string"},
        "question_text": {"type": "string"},
        "question_type": {"type": "string"},
        "answer_context": {
            "type": "array",
            "items": {"type": "string"}
        },
        "answer_text": {
            "type": "array",
            "items": {"type": "string"}
        }
    },
    "additionalProperties": False
}

def upload_file_in_openai(file) -> dict:
    """
    Upload File in OpenAI
    """
    try:
        file_obj = openai.files.create(file=open(file.name, "rb"), purpose="assistants")
        return file_obj
    except Exception as e:
        raise e

def create_openai_assistant(assistant_name: str, instructions: str, openai_model: str, file_ids: list):
    """
    Create OPENAI Assistant for File Search Functionality
    
    @parameters:
        - assistant_name (str) : Assistant Name
        - instructions (str) : Instructions to Assistant
        - openai_model (str) : OpenAI's Model Name
        - file_ids (list) : List of file IDs to attach to the assistant
        
    @returns:
        - OpenAI's Assistant Object
    """
    return openai.beta.assistants.create(
      name=assistant_name,
      instructions=instructions,
      model=openai_model,
      tools=[{"type": "retrieval"}],  # Using retrieval tool instead of file_search
      file_ids=file_ids,  # Directly attach files to the assistant
      temperature=0.0,
    )

def create_thread_and_run(assistant_id: str, user_content: str):
    """
    Create Thread and Run for File Search Functionality
    
    @parameters:
        - assistant_id (str) : Assistant Id
        - user_content (str) : User Role's Content/Query
        
    @returns:
        - OpenAI's Run Object
    """
    return openai.beta.threads.create_and_run(
          assistant_id=assistant_id,
          thread={
            "messages": [
             {
                  "role": "user",
                  "content": user_content
            }
          ]
          },
        )
        
def get_response_messages_by_thread_id(run_object, max_wait_seconds=120, poll_interval=1):
    """
    Polls the OpenAI run until completion, failure, or timeout. Returns all completed messages so far.
    """
    waited = 0
    while waited < max_wait_seconds:
        run_info = openai.beta.threads.runs.retrieve(thread_id=run_object.thread_id, run_id=run_object.id)
        if run_info.completed_at or run_info.failed_at:
            break
        time.sleep(poll_interval)
        waited += poll_interval
    else:
        # Timeout reached: fetch whatever messages are available
        logger.warning(f"Timeout reached waiting for run {run_object.id}. Returning partial results.")
    # Fetch all messages (not just the latest)
    messages = openai.beta.threads.messages.list(
        thread_id=run_object.thread_id,
        run_id=run_object.id,
        order="asc",
        limit=100  # adjust as needed
    )
    # Concatenate all message contents
    responses = []
    for msg in messages:
        if msg.content and hasattr(msg.content[0], 'text'):
            responses.append(msg.content[0].text.value)
    return "\n".join(responses)

def delete_all_openai_objects(file_ids_list: list, run_ids: list, assistant_id: str):
    """
    Delete all OpenAI Objects
    """
    try:
        # Delete Uploaded Files
        for file_id in file_ids_list:
            try:
                openai.files.delete(file_id)
            except Exception as e:
                continue
            
        # Delete All Messages and Thread
        for run_obj in run_ids:
            thread_id = run_obj.thread_id
            
            # Delete Messages
            thread_messages = openai.beta.threads.messages.list(thread_id)
            for message in thread_messages.data:
                openai.beta.threads.messages.delete(
                    message_id=message.id,
                    thread_id=thread_id
                )
                
            # Delete Thread
            openai.beta.threads.delete(thread_id)
        
        # Delete Assistant
        openai.beta.assistants.delete(assistant_id)
        
    except Exception as e:
        raise e
    
def get_llm_response(assistant_id: str, user_content: str) -> str:
    """
    Get Formatted LLM Response
    """
    run = create_thread_and_run(assistant_id=assistant_id, user_content=user_content)

    # Get Messages from LLM
    response = get_response_messages_by_thread_id(run_object=run)

    return {"run_object": run, "response": response }
    
def format_json_response_using_llm(raw_string: str):
    """
    Convert to structured JSON format from string using LLM
    """
    try:
        response = openai.ChatCompletion.create(
            model="gpt-4-turbo",
            response_format={ "type": "json_object" },
            messages=[
                {
                    "role": "system",
                    "content": """
    You are a helpful assistant designed to output JSON from raw string with below format : 
    
    ### OUTPUT FORMAT :
    ```
    "response" : {
        [
            {
                "question_code": <question_code>,
                "question_text": <question_text>,
                "question_type": <question_type>,
                "answer_context": <answer_context>,
                "answer_text": <answer_text>,
                "answer_code": <answer_code>
            },
            {
                "question_code": <question_code>,
                "question_text": <question_text>,
                "question_type": <question_type>,
                "answer_context": <answer_context>,
                "answer_text": <answer_text>,
                "answer_code": <answer_code>
            }
        ]
    }
    ```
    """
                },
                {"role": "user", "content": raw_string}
            ]
        )
        
        json_data = json.loads(response.choices[0].message.content)
    
        if "response" in json_data.keys():
            return json_data.get("response")
            
        elif "result" in json_data.keys():
            return json_data.get("result")
            
        elif "question_data" in json_data.keys():
            return json_data.get("question_data")
            
        else:
            return json_data
        
    except Exception as e:
        raise e        
    

# Define a helper function to split the assessment list into chunks
def split_assessment_list(assessment_list, chunk_size):
    """Splits a list into chunks of a given size."""
    return [assessment_list[i:i + chunk_size] for i in range(0, len(assessment_list), chunk_size)]

# Define a helper function to prepare user content
def prepare_user_content(splitted_list, template):
    """Prepares user content by replacing placeholders in the template."""
    user_content_list = []
    for item in splitted_list:
        user_content_copy = template.replace("{{question_list_in_json}}", json.dumps(item))
        user_content_copy = user_content_copy.replace("{{todays_date - 1 year}}", str(datetime.now().strftime("%m/%d/%Y"))[:10])
        user_content_copy = user_content_copy.replace("{{todays_date}}", str(datetime.now().strftime("%m/%d/%Y"))[:10])
        user_content_list.append(user_content_copy)
    return user_content_list

def pre_process_questions(questions:list):
    for question in questions:
        # remove the answer_context from each question if it exists
        if "answer_context" in question:
            question.pop("answer_context")
            
        # Check if the question has options and options is an array
        if "options" in question and isinstance(question["options"], list):
            # Add "Not Available" to options if it's not already in the list
            if "Not Available" not in question["options"]:
                question["options"].append("Not Available")
    
    return questions


def answer_questions(conversation_data:dict, questions:list, client_id: str, assessment_id: str):
    settings: AiCoreConfig = get_settings()
    openai.api_key = settings.openai_key
    questions = pre_process_questions(questions)
    logger.info(f"question count: {len(questions)}")
    try:
        assistant_name = f"assistant_{client_id}"
        json_content = json.dumps(conversation_data)
        with tempfile.NamedTemporaryFile(delete=False, suffix=".json") as temp_file:
            temp_file.write(json_content.encode("utf-8"))
            temp_file_path = temp_file.name
        # 1. Create a vector store
        vector_store = openai.beta.vector_stores.create(name=f"vs_{client_id}")
        # 2. Upload file to vector store
        with open(temp_file_path, "rb") as file:
            file_batch = openai.beta.vector_stores.file_batches.upload_and_poll(
                vector_store_id=vector_store.id,
                files=[file]
            )
        logger.info(f"File batch status: {file_batch.status}")
        logger.info(f"File batch counts: {file_batch.file_counts}")
        # 3. Create the assistant with file_search tool and vector_store_id
        assistant = openai.beta.assistants.create(
            name=assistant_name,
            instructions=INSTRUCTIONS,
            model=settings.openai_model,
            tools=[{"type": "file_search"}],
            tool_resources={
                "file_search": {
                    "vector_store_ids": [vector_store.id]
                }
            },
            temperature=0.0,
        )
        chunk_size = number_of_question
        assessment_splitted_list = split_assessment_list(questions, chunk_size)
        user_content_list = prepare_user_content(assessment_splitted_list, user_content_default)
        question_answer_list = []
        with ThreadPoolExecutor() as executor:
            futures = []
            for user_content in user_content_list:
                future = executor.submit(get_llm_response, assistant.id, user_content)
                futures.append(future)
            question_answer_list = []
            run_obj_list = []
            for future in futures:
                try:
                    data_dict = future.result()
                    run_object = data_dict.get("run_object")
                    run_obj_list.append(run_object)
                    response = data_dict.get("response")
                    if response and response.find("Robert") != -1:
                        print("The string contains 'Robert'.")
                    if response:
                        string = response.replace("```", "").replace("json", "").strip()
                        try:
                            json_data = json.loads(string)
                            logger.info(f"type of json_data: {type(json_data)}")
                            if isinstance(json_data, list):
                                jsonschema.validate(instance=json_data, schema=response_schema)
                                for item in json_data:
                                    question_answer_list.append(item)
                            else:
                                jsonschema.validate(instance=json_data, schema=single_response_schema)
                                question_answer_list.append(json_data)
                                logger.info(f"json_data is not a list. {json_data}")
                        except Exception as ve:
                            logger.error(f"JSON does not match expected schema: {ve}")
                            send_slack_message(f"JSON does not match expected schema: {ve}")
                except Exception as e:
                    logger.error(f"Error processing future: {e}")
        if delete_openai_objects:
            try:
                openai.beta.assistants.delete(assistant.id)
                openai.beta.vector_stores.delete(vector_store.id)
            except Exception as e:
                logger.error(f"Error during cleanup: {e}")
        slack_message = (f"Whole process completed successfully with Question-Answer pair.\n\n"
        f"Date : *{time.ctime(time.time())}*\n"
        f"Client ID : *{client_id}*\n"
        f"Assessment ID : *{assessment_id}*")
        send_slack_message(slack_message)
        final_data_dict = {
            "ClientID": client_id,
            "Responses": question_answer_list
        }
        return final_data_dict
    except Exception as e:
        error_message = (
            f"Something went wrong while generating question-answer file.\n\n"
            f"Date : *{time.ctime(time.time())}*\n"
            f"Client ID : *{client_id}*\n"
            f"Assessment ID : *{assessment_id}*\n"
            f"Error : *{str(e)}*\n"
            f"Line no. : *{e.__traceback__.tb_lineno}*"
        )
        logger.error(error_message)
        send_slack_message(error_message)
        return None
        

def main():
    try:
        with open(os.path.join("tests","data","conversation.json"), "r", encoding="utf-8") as file:
            conversation_data = json.load(file)    
            answers = answer_questions(conversation_data, client_id="kate", assessment_id="assessment-1")
            print(answers)
            with open(os.path.join(f"answers-{time.time()}.json"), "w", encoding="utf-8") as file:
                json.dump(answers, file)
    except Exception as e:
        print(e)

if __name__ == "__main__":
    main()
