import logging
from config import get_settings
from ai_core.models.config import Config as Ai<PERSON>oreConfig
from slack_sdk import WebClient
logger = logging.getLogger(__name__)

slack_client = None


# FIXME - make a packages/messaging package
def send_slack_message(message: str):
    """
    Send a message to Slack Channel using Bot User
    """
    global slack_client
    settings: AiCoreConfig = get_settings()

    try:
        if slack_client is None and settings.slack_enabled:
            slack_client = WebClient(token=settings.slack_bot_token)

        if slack_client is None:
            logger.info(f"would be sending slack message: {message}")
        else:
            logger.info(f"sending slack message: {message}")


        # if settings.use_slack
        if slack_client:
            slack_client.chat_postMessage(
                channel=settings.slack_channel,
                mrkdwn=True, 
                text=f"{settings.slack_prefix}\n\n{message}"
            )
    except Exception as e:
        logger.error(f"sending slack failed {e}")
