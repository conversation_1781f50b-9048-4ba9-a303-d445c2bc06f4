#!/usr/bin/env python3
"""
Test the API key fix for clinician observation generation
"""

import os
import sys

# Add the current directory to the path so we can import the module
sys.path.insert(0, os.path.dirname(__file__))

def test_api_key_handling():
    """Test that the API key handling works correctly"""
    print("Testing API key handling in clinician observation generation...")
    
    try:
        # Import the function
        from answer_questions_langchain_knowledgebase_subgrouping import generate_clinician_observations
        
        # Create a mock RAG system without API key
        class MockRAGSystemNoKey:
            def __init__(self):
                self.vectorstore = None
        
        # Test with no API key
        mock_rag_no_key = MockRAGSystemNoKey()
        result = generate_clinician_observations(mock_rag_no_key, "Functional Abilities")
        
        if result == "":
            print("✅ Function correctly handles missing API key")
        else:
            print(f"❌ Expected empty string for missing API key, got: {result}")
            return False
        
        # Create a mock RAG system with API key but no vectorstore
        class MockRAGSystemWithKey:
            def __init__(self):
                self.openai_api_key = "test-key"
                self.vectorstore = None
        
        # Test with API key but no vectorstore
        mock_rag_with_key = MockRAGSystemWithKey()
        result = generate_clinician_observations(mock_rag_with_key, "Functional Abilities")
        
        if result == "":
            print("✅ Function correctly handles missing vectorstore")
        else:
            print(f"❌ Expected empty string for missing vectorstore, got: {result}")
            return False
        
        # Test with disabled group
        result = generate_clinician_observations(mock_rag_with_key, "Non-existent Group")
        
        if result == "":
            print("✅ Function correctly handles disabled/non-existent group")
        else:
            print(f"❌ Expected empty string for disabled group, got: {result}")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_configuration_loading():
    """Test that configuration loading still works"""
    print("\nTesting configuration loading...")
    
    try:
        from answer_questions_langchain_knowledgebase_subgrouping import load_clinician_observation_config
        
        config = load_clinician_observation_config()
        
        if config and "clinician_observation_config" in config:
            print("✅ Configuration loads successfully")
            
            obs_config = config["clinician_observation_config"]
            functional_abilities = obs_config.get("Functional Abilities", {})
            
            if functional_abilities.get("enabled", False):
                print("✅ Functional Abilities group is enabled")
                
                search_queries = functional_abilities.get("search_queries", [])
                if len(search_queries) > 0:
                    print(f"✅ Found {len(search_queries)} search queries")
                    print(f"  - Example query: {search_queries[0]}")
                else:
                    print("❌ No search queries found")
                    return False
            else:
                print("❌ Functional Abilities group is not enabled")
                return False
        else:
            print("❌ Configuration loading failed")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing API Key Fix and Configuration\n")
    print("=" * 60)
    
    success = True
    
    # Test API key handling
    if not test_api_key_handling():
        print("\n❌ API key handling test failed")
        success = False
    
    # Test configuration loading
    if not test_configuration_loading():
        print("\n❌ Configuration loading test failed")
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✅ All tests completed successfully!")
        print("\n📝 The API key issue has been fixed.")
        print("📝 The system will now properly use the RAG system's API key.")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    exit(main())
