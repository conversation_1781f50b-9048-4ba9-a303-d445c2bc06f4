import logging
from fastapi import Request, APIRouter, HTTPException, Depends, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List, Union
import uuid
from config import create_app_settings
from database.interface import DatabaseAdapter
from database.factory import get_database, get_db
from queues.factory import get_queue_client
from queues.interface import QueueClient
#from ai_core.models.transcription_rating import TranscriptionRating
from ai_core.models.config import Config
from ai_core.models.transcription_rating import TranscriptionRating

from typing import Dict
from auth.factory import get_auth_provider
from ai_core.auth_util import require_role, no_role_required
from ai_core.invoker import safe_invoke
from ai_core.service_response import ServiceResponseMessage

settings, config_provider = create_app_settings("ai_core", Config)


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

auth = get_auth_provider(config_provider)
security = HTTPBearer()

def get_db_provider() -> DatabaseAdapter:
    return get_database(config_provider)

# Inject database dependency dynamically
def get_queue() -> QueueClient:

    queue_type = getattr(settings, "queue_type", False)
    if queue_type == None or queue_type == False:
        return None

    q_params: Dict[str, str] = {}

    if queue_type == "local":
        q_params = {}
    elif queue_type == "sqs":
        q_params = {
           # aws stuff
        }

    return get_queue_client(name="transcription_rating", queue_type="local", **q_params)  # Pass to factory



# write - Create an item
@router.post("/transcription-rating", response_model=Union[TranscriptionRating, ServiceResponseMessage])
def create_transcription_rating(item: TranscriptionRating, 
                        request: Request,
                        db: DatabaseAdapter = Depends(get_db_provider), 
                        q: QueueClient = Depends(get_queue), 
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"--> create_transcription_rating: {item}")
    try:
        ret = safe_invoke("ai_core.services.transcription_rating_service", "create_transcription_rating", [item, db, q, user, request])
    except Exception as e:
        logger.error(f"Error creating transcription_rating: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


# read - Retrieve all items
@router.get("/transcription-rating-list", response_model=Union[List[TranscriptionRating]])
def get_all_transcription_ratings(request:Request, db: DatabaseAdapter = Depends(get_db_provider),
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug("--> get_all_transcription_ratings")
    try:
        ret = safe_invoke("ai_core.services.transcription_rating_service", "get_all_transcription_rating", [db, user, request])
    except Exception as e:
        logger.error(f"Error retrieving all transcription_ratings: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


# read - Retrieve a single item
@router.get("/transcription-rating/{id}", response_model=Union[TranscriptionRating, ServiceResponseMessage])
def get_transcription_rating(id: str, request:Request,
                     db: DatabaseAdapter = Depends(get_db_provider), 
                     user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to retrieve transcription_rating with id: {id}")
    try:
        ret =safe_invoke("ai_core.services.transcription_rating_service", "get_transcription_rating", [id, db, user, request])
    except Exception as e:
        logger.error(f"Error retrieving transcription_rating with id: {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    logger.debug(f"Retrieved transcription_rating: {ret}")
    return ret

# read - Retrieve a single item
@router.get("/transcription-rating-path/{full_path:path}", response_model=Union[TranscriptionRating, ServiceResponseMessage])
def get_transcription_rating(full_path: str, request:Request,
                     db: DatabaseAdapter = Depends(get_db_provider), 
                     user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to retrieve transcription_rating with full_path: {full_path}")
    try:
        ret =safe_invoke("ai_core.services.transcription_rating_service", "get_path_transcription_rating", [full_path, db, user, request])
    except Exception as e:
        logger.error(f"Error retrieving transcription_rating with full_path: {full_path}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    logger.debug(f"Retrieved transcription_rating: {ret}")
    return ret

# write - Update an item (without modifying ID)
@router.put("/transcription-rating/{id}", response_model=Union[TranscriptionRating, ServiceResponseMessage])
def update_transcription_rating(id: str, request:Request,
                        updated_item: TranscriptionRating, 
                        db: DatabaseAdapter = Depends(get_db_provider), 
                        q: QueueClient = Depends(get_queue), 
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    item = db.get_item("transcription_rating", id)
    logger.debug(f"Received request to update transcription_rating with id {id}: {updated_item}")
    try:
        ret = safe_invoke("ai_core.services.transcription_rating_service", "update_transcription_rating", [id, updated_item, db, q, user, request])
    except Exception as e:
        logger.error(f"Error updating transcription_rating with id {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret

# write - Delete an item
@router.delete("/transcription-rating/{id}", response_model=Union[TranscriptionRating, ServiceResponseMessage])
def delete_transcription_rating(id: str, request:Request,
                        db: DatabaseAdapter = Depends(get_db_provider), 
                        q: QueueClient = Depends(get_queue), 
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to delete transcription_rating with id {id}")
    try:
        ret = safe_invoke("ai_core.services.transcription_rating_service", "delete_transcription_rating", [id, db, q, user, request])
    except Exception as e:
        logger.error(f"Error deleting transcription_rating with id {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        logger.warning(f"TranscriptionRating with id {id} not found")
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret
