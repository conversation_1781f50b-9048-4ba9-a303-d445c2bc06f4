import openai
import os

# Initialize the client
api_key = os.environ.get('OPENAI_API_KEY')
if not api_key:
    raise RuntimeError('OPENAI_API_KEY environment variable not set')
client = openai.OpenAI(api_key=api_key)

# List files in your OpenAI storage
response = client.files.list()

# Initialize a counter for the total number of files
file_count = 0

# List to store file IDs for deletion
file_ids = []

# Iterate over the files and count them
for file in response:
    file_count += 1
    file_ids.append(file.id)
    print(f"ID: {file.id}, Filename: {file.filename}, Purpose: {file.purpose}")

# Print the total number of files
print(f"Total number of files: {file_count}")

# Ask user if they want to delete all files
delete_all = input("Would you like to delete all files? (Y/N): ").strip().upper()

if delete_all == 'Y':
    # Iterate over file IDs and delete each file
    for file_id in file_ids:
        client.files.delete(file_id)
        print(f"Deleted file with ID: {file_id}")
    print("All files have been deleted.")
else:
    print("No files were deleted.")

# Ask user if they want to delete all assistants
try:
    assistants = client.beta.assistants.list()
    assistant_ids = [a.id for a in assistants]
    print(f"Found {len(assistant_ids)} assistants.")
    if assistant_ids:
        delete_assistants = input("Would you like to delete all assistants? (Y/N): ").strip().upper()
        if delete_assistants == 'Y':
            for assistant_id in assistant_ids:
                client.beta.assistants.delete(assistant_id)
                print(f"Deleted assistant with ID: {assistant_id}")
            print("All assistants have been deleted.")
        else:
            print("No assistants were deleted.")
    else:
        print("No assistants found.")
except Exception as e:
    print(f"Error listing or deleting assistants: {e}")

# Delete all vector stores
try:
    vector_stores = client.beta.vector_stores.list()
    vector_store_ids = [vs.id for vs in vector_stores]
    print(f"Found {len(vector_store_ids)} vector stores.")
    if vector_store_ids:
        delete_vector_stores = input("Would you like to delete all vector stores? (Y/N): ").strip().upper()
        if delete_vector_stores == 'Y':
            for vs_id in vector_store_ids:
                client.beta.vector_stores.delete(vs_id)
                print(f"Deleted vector store with ID: {vs_id}")
            print("All vector stores have been deleted.")
        else:
            print("No vector stores were deleted.")
    else:
        print("No vector stores found.")
except Exception as e:
    print(f"Error listing or deleting vector stores: {e}")