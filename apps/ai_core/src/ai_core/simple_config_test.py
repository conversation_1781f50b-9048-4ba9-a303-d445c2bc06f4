#!/usr/bin/env python3
"""
Simple test for clinician observation configuration without import conflicts
"""

import json
import os

def test_config_file():
    """Test that the configuration file exists and is valid JSON"""
    print("Testing clinician observation configuration file...")
    
    config_path = "clinician_observation_config.json"
    
    if not os.path.exists(config_path):
        print(f"❌ Configuration file not found: {config_path}")
        return False
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        print("✅ Configuration file loaded successfully")
        
        # Check main structure
        if "clinician_observation_config" not in config:
            print("❌ Missing 'clinician_observation_config' key")
            return False
        
        print("✅ Main configuration key found")
        
        obs_config = config["clinician_observation_config"]
        
        # Check some expected groups
        expected_groups = ["Functional Abilities", "Cognitive Status", "ADLs Assessment"]
        
        for group in expected_groups:
            if group in obs_config:
                group_config = obs_config[group]
                print(f"✅ Found configuration for '{group}'")
                
                # Check required fields
                required_fields = ["enabled", "search_queries", "observation_prompt"]
                missing_fields = [field for field in required_fields if field not in group_config]
                
                if not missing_fields:
                    print(f"  ✅ All required fields present")
                    print(f"  - Enabled: {group_config['enabled']}")
                    print(f"  - Search queries: {len(group_config['search_queries'])} queries")
                    print(f"  - Observation prompt length: {len(group_config['observation_prompt'])} chars")
                else:
                    print(f"  ❌ Missing fields: {missing_fields}")
            else:
                print(f"❌ Missing configuration for '{group}'")
        
        # Show summary
        total_groups = len(obs_config)
        enabled_groups = sum(1 for group_config in obs_config.values() if group_config.get("enabled", False))
        
        print(f"\n📊 Summary:")
        print(f"  - Total configured groups: {total_groups}")
        print(f"  - Enabled groups: {enabled_groups}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in configuration file: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading configuration file: {e}")
        return False

def test_group_mappings():
    """Test OASIS group mappings manually"""
    print("\nTesting OASIS group mappings...")
    
    # Define the OASIS group map manually for testing
    OASIS_GROUP_MAP = {
        "A1000–A2300": "Patient Demographics",
        "M0080–M0102": "Assessment Info",
        "M1000–M1100": "Patient History",
        "M1028–M1060": "Health Conditions",
        "J0510–J0530": "Pain Assessment",
        "B0200–B1300": "Vision & Communication",
        "C0100–C0500": "Cognitive Status",
        "C1310": "Delirium Assessment",
        "D0150–D0700": "Mood/Behavior",
        "M1306–M1342": "Skin/Wound Assessment",
        "M1400": "Respiratory Status",
        "M1600–M1630": "Elimination Status",
        "M1700–M1745": "Cognitive Function",
        "M1800–M1870": "ADLs Assessment",
        "GG0100–GG0170": "Functional Abilities",
        "G0900": "Functional Support",
        "N0415–N2005": "Medications",
        "M1033": "Risk Assessment",
        "O0110–O0420": "Treatments",
        "Z0400–Z0500": "Clinician Sign-Off"
    }
    
    # Simple test for some mappings
    test_cases = [
        ("GG0130", "Functional Abilities"),
        ("C0100", "Cognitive Status"),
        ("M1800", "ADLs Assessment"),
        ("D0150", "Mood/Behavior"),
        ("J0510", "Pain Assessment")
    ]
    
    print("✅ OASIS group mappings defined")
    print(f"✅ Total group ranges: {len(OASIS_GROUP_MAP)}")
    
    for code, expected_group in test_cases:
        print(f"  - Test case: {code} should map to '{expected_group}'")
    
    return True

def main():
    """Run all tests"""
    print("🧪 Simple Clinician Observation Configuration Test\n")
    print("=" * 60)
    
    success = True
    
    # Test configuration file
    if not test_config_file():
        print("\n❌ Configuration file test failed")
        success = False
    
    # Test group mappings
    if not test_group_mappings():
        print("\n❌ Group mapping test failed")
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✅ All tests completed successfully!")
        print("\n📝 Note: This is a basic configuration test.")
        print("📝 Full integration testing requires the main system to be running.")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    exit(main())
