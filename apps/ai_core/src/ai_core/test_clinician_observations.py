#!/usr/bin/env python3
"""
Test script for clinician observation configuration and generation
"""

import json
import os
import sys

# Add the current directory to the path so we can import the module
sys.path.insert(0, os.path.dirname(__file__))

from answer_questions_langchain_knowledgebase_subgrouping import (
    load_clinician_observation_config,
    get_oasis_group,
    OASIS_GROUP_MAP
)

def test_config_loading():
    """Test loading the clinician observation configuration"""
    print("Testing clinician observation configuration loading...")
    
    config = load_clinician_observation_config()
    
    if not config:
        print("❌ Failed to load configuration")
        return False
    
    print("✅ Configuration loaded successfully")
    
    # Check if the main config key exists
    if "clinician_observation_config" not in config:
        print("❌ Missing 'clinician_observation_config' key")
        return False
    
    print("✅ Main configuration key found")
    
    # Check some expected groups
    expected_groups = ["Functional Abilities", "Cognitive Status", "ADLs Assessment"]
    obs_config = config["clinician_observation_config"]
    
    for group in expected_groups:
        if group in obs_config:
            group_config = obs_config[group]
            print(f"✅ Found configuration for '{group}'")
            
            # Check required fields
            if "enabled" in group_config and "search_queries" in group_config and "observation_prompt" in group_config:
                print(f"  ✅ All required fields present for '{group}'")
                print(f"  - Enabled: {group_config['enabled']}")
                print(f"  - Search queries: {len(group_config['search_queries'])} queries")
                print(f"  - Observation prompt length: {len(group_config['observation_prompt'])} chars")
            else:
                print(f"  ❌ Missing required fields for '{group}'")
        else:
            print(f"❌ Missing configuration for '{group}'")
    
    return True

def test_group_mapping():
    """Test OASIS group mapping for different question codes"""
    print("\nTesting OASIS group mapping...")
    
    test_codes = [
        ("GG0130", "Functional Abilities"),
        ("C0100", "Cognitive Status"),
        ("M1800", "ADLs Assessment"),
        ("D0150", "Mood/Behavior"),
        ("J0510", "Pain Assessment"),
        ("M1306", "Skin/Wound Assessment"),
        ("N0415", "Medications"),
        ("B0200", "Vision & Communication"),
        ("M1400", "Respiratory Status"),
        ("M1600", "Elimination Status")
    ]
    
    for code, expected_group in test_codes:
        actual_group = get_oasis_group(code)
        if actual_group == expected_group:
            print(f"✅ {code} -> {actual_group}")
        else:
            print(f"❌ {code} -> Expected: {expected_group}, Got: {actual_group}")

def test_config_structure():
    """Test the structure of the configuration file"""
    print("\nTesting configuration file structure...")
    
    config = load_clinician_observation_config()
    obs_config = config.get("clinician_observation_config", {})
    
    total_groups = len(obs_config)
    enabled_groups = sum(1 for group_config in obs_config.values() if group_config.get("enabled", False))
    
    print(f"📊 Total configured groups: {total_groups}")
    print(f"📊 Enabled groups: {enabled_groups}")
    
    # Show all configured groups
    print("\n📋 All configured groups:")
    for group_name, group_config in obs_config.items():
        status = "✅ Enabled" if group_config.get("enabled", False) else "❌ Disabled"
        query_count = len(group_config.get("search_queries", []))
        print(f"  {group_name}: {status} ({query_count} search queries)")

def main():
    """Run all tests"""
    print("🧪 Testing Clinician Observation Configuration\n")
    print("=" * 60)
    
    try:
        # Test configuration loading
        if not test_config_loading():
            print("\n❌ Configuration loading test failed")
            return 1
        
        # Test group mapping
        test_group_mapping()
        
        # Test configuration structure
        test_config_structure()
        
        print("\n" + "=" * 60)
        print("✅ All tests completed successfully!")
        return 0
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
