import logging
import subprocess
import psutil
import threading
import requests
import json
import os
import git
import traceback
import asyncio
import time
import yaml
import shutil
from typing import Dict

from ai_core.slack import send_slack_message

from queues.interface import QueueClient
from ai_core.models.transcription_request import TranscriptionRequest
from ai_core.services.transcription_request_service import create_transcription_request

from database.factory import get_database
from database.interface import DatabaseAdapter

from config import create_app_settings
from ai_core.models.config import Config as AiCoreConfig

import uvicorn
from uvicorn import Config, Server
from database.interface import DatabaseAdapter
from database.factory import get_database
from queues.factory import get_queue_client
from datetime import datetime

logger = logging.getLogger(__name__)


class Pipeline:
    def __init__(self, settings: AiCoreConfig):
        """
        Initializes the pipeline, including database provider and queue client.
        """
        self.settings: AiCoreConfig = settings
        self.db = self.get_db_provider()
        # self.queue_client = get_queue_client("run", config_provider_fn=lambda: self.settings.model_dump())
        self.port = self.settings.port
        self.ssl_enabled = self.settings.ssl_enabled        

    def get_db_provider(self) -> DatabaseAdapter:
        """Returns the database provider instance."""
        return get_database(lambda: self.settings.model_dump())
    
    def get_queue(self, name: str) -> QueueClient:
        queue_type =self.settings.queue_type  # Read from app config

        q_params: Dict[str, str] = {}

        if not queue_type:
            return None

        if queue_type == "local":
            q_params = {}
        elif queue_type == "sqs":
            q_params = {
            "aws_access_key_id":self.settings.aws_access_key_id,
            "aws_secret_access_key":self.settings.aws_secret_access_key
            }

        return get_queue_client(name, queue_type, **q_params)  # Pass to factory


    async def run(self):
        """Starts listening to the queue and processing messages."""
        # Get the queue client instance
        queue_input_client = self.get_queue(self.settings.queue_input_name)
        queue_output_client = self.get_queue(self.settings.queue_output_name)

        logger.info(f"Listening on queue '{self.settings.queue_input_name}' of type '{self.settings.queue_type}'...")
        while True:
            request = None

            try:
                # Receive and process messages
                message = queue_input_client.receive_message()
                if message:
                    logger.info(f"received message: {message}")
                    

                    # FIXME - There should be a common metadata field for all messages & data field name
                    # No Common MetaData or Field Name For Actual Data !
                    # AWS SQS sends message in "Body" field
                    body = message["Body"]
                    recipient_handle = message["ReceiptHandle"]

                    # No retrying - for now otherwise put this after the processing
                    queue_input_client.delete_message(recipient_handle)
                    logger.info(f"deleted message with handle {recipient_handle}")

                    request = TranscriptionRequest.from_json(body)
                    logger.info(f"transcription request: {request.model_dump_json()}")
                    
                    logger.info(f"starting transcription {request.id}")
                    send_slack_message(f"Recieved transcription request for: {request.id}")
                    create_transcription_request(request, self.get_db_provider(), None, None, None)
                    logger.info(f"procesed transcription {request.id}")

                    message_body = request.model_dump_json()  # Converts Pydantic model to a JSON string
                    logger.info(f"sending message to output queue: {message_body}")
                    queue_output_client.send_message(message_body)
                    
                await asyncio.sleep(1)

            except Exception as e:
                logger.error("An error occurred", exc_info=True)
                send_slack_message(f"🚨 ERROR: request {request}  {e}, traceback: {traceback.format_exc()}")
                if request:
                    request.status = "error"
                    request.completed = int(time.time())
                    request.exception = str(e)
                    queue_output_client.send_message(request.model_dump_json())
                # prevent cpu thrashing
                await asyncio.sleep(1)

if __name__ == "__main__":
    settings, _ = create_app_settings("ai_core", AiCoreConfig)
    logger.info(f"App Setting: {json.dumps(settings.model_dump(), indent=2)}")

    app_server = Pipeline(settings)
    import asyncio
    asyncio.run(app_server.run())
