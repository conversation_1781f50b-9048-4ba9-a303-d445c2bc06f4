import pytest
import os
import requests
import datetime
from datetime import timezone
from pathlib import Path
from config import create_app_settings
from ai_core.models.config import Config as AiCoreConfig

def find_repo_root(marker=".repo-root"):
    """Find the monorepo root by searching for a marker file up the directory tree."""
    current = Path(__file__).resolve().parent
    for parent in [current] + list(current.parents):
        if (parent / marker).is_file():
            return parent
    raise RuntimeError(f"Could not find repo root with marker: {marker}")

# Usage:
REPO_ROOT = find_repo_root()
print("REPO_ROOT", REPO_ROOT)

env = os.getenv("ENV", "local")

#env = "dev"
# env = "local"

if env == "prod":
    env_file = f"{REPO_ROOT}/apps/ai_core/.env.prod"
elif env == "dev":
    env_file = f"{REPO_ROOT}/apps/ai_core/.env.dev"
else:
    env_file = f"{REPO_ROOT}/apps/ai_core/.env"

config, _ = create_app_settings("ai_core", AiCoreConfig, env_file=env_file)
config: AiCoreConfig = config

if env == "prod":
    BASE_URL = f"http://*************:{config.port}/v1"
elif env == "dev":
    BASE_URL = f"http://************:{config.port}/v1"
    COMPANY_ID = "dev1"
    USER_ID = f"john@{COMPANY_ID}.com"
else:
    BASE_URL = f"http://localhost:{config.port}/v1"
    COMPANY_ID = "dev1"
    USER_ID = f"john@{COMPANY_ID}.com"


VISIT_ID = "visit-1234"
CLIENT_ID = "client_id-*********"
ASSESSMENT_ID = "assessment-1234"
TRANSCRIBE_TYPE = "deepgram"
QUESTION_FILES = ["questionForm.json"]
MODE = "all-stages" # "answer-only" or "all-stages"

TIMESTAMP = datetime.datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S") 

TEST_PAYLOADS = [
    {
        "assessment_id": f"local-brandon-13m",
        "audio_files": ["recording-1750708428773.m4a"],
        "question_files": QUESTION_FILES,
        "id": None,  # Will be set in test
        "user_id": USER_ID,
        "visit_id": VISIT_ID,
        "client_id": CLIENT_ID,
        "company_id": COMPANY_ID,
        "transcribe_type": TRANSCRIBE_TYPE,
        "mode": MODE
    }
    # {
    #     "assessment_id": f"Kate-1min.30sec",
    #     "audio_files": ["Kate-08-13-24-1min.30sec.webm"],
    #     "question_files": QUESTION_FILES,
    #     "id": None,  # Will be set in test
    #     "user_id": USER_ID,
    #     "visit_id": VISIT_ID,
    #     "client_id": CLIENT_ID,
    #     "company_id": COMPANY_ID,
    #     "transcribe_type": TRANSCRIBE_TYPE,
    #     "mode": MODE
    # }
    # ,
    # {
    #     "assessment_id": f"Kate-49min26sec",
    #     "audio_files": ["Kate-SOC-Visit-Audio-49min26sec.webm"],
    #     "question_files": QUESTION_FILES,
    #     "id": None,
    #     "user_id": USER_ID,
    #     "visit_id": VISIT_ID,
    #     "client_id": CLIENT_ID,
    #     "company_id": COMPANY_ID,
    #     "transcribe_type": TRANSCRIBE_TYPE,
    #     "mode": MODE
    # }
]

import database.factory as db_factory

@pytest.mark.integration
def test_transcription_request_env():
    timestamp = datetime.datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")

    if env == "local":
        # because the cwd is not the same when using the debugger
        config.database_dir = f"{REPO_ROOT}/apps/ai_core/data/filesystem_db"
    print("DEBUG: config passed to db_factory.get_database:", config)
    db = db_factory.get_database(lambda: config.model_dump())

    for test_payload in TEST_PAYLOADS:
        assessment_id = test_payload["assessment_id"]
        new_test = f"{env}-{assessment_id}-{timestamp}"
        print(f"REPO_ROOT {REPO_ROOT}")
        db.copy_table(f"{COMPANY_ID}/{VISIT_ID}/{assessment_id}/input", f"{COMPANY_ID}/{VISIT_ID}/{new_test}/input")
        # Fill in the rest of the payload fields
        payload = test_payload.copy()
        payload.update({
            "id": new_test,
            "user_id": USER_ID,
            "visit_id": VISIT_ID,
            "assessment_id": new_test,
            "client_id": CLIENT_ID,
            "company_id": COMPANY_ID,
            "transcribe_type": TRANSCRIBE_TYPE,
            "mode": MODE
        })
        url = f"{BASE_URL}/transcription-request"
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json"
        }
        response = requests.post(url, json=payload, headers=headers)
        assert response.status_code == 200, f"Unexpected status code: {response.status_code}, body: {response.text}"
        data = response.json()
        assert "id" in data
        assert data["id"] == payload["id"]
        assert data["user_id"] == payload["user_id"]

        db.insert_item("results", f"{new_test}.json", data)

        all_results = db.get_item("results", "all-results.json")
        if not all_results:
            all_results = []
        all_results.append(data)
        db.insert_item("results", "all-results.json",  all_results)

        print(f"results: {data}")

if __name__ == "__main__":
    import database.factory as db_factory
    timestamp = datetime.datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
    if env == "local":
        config.database_dir = f"{REPO_ROOT}/apps/ai_core/data/filesystem_db"
    print("DEBUG: config passed to db_factory.get_database:", config)
    db = db_factory.get_database(lambda: config.model_dump())
    failed = False
    for test_payload in TEST_PAYLOADS:
        assessment_id = test_payload["assessment_id"]
        new_test = f"{env}-{assessment_id}-{timestamp}"
        print(f"REPO_ROOT {REPO_ROOT}")
        db.copy_table(f"{COMPANY_ID}/{VISIT_ID}/{assessment_id}/input", f"{COMPANY_ID}/{VISIT_ID}/{new_test}/input")
        payload = test_payload.copy()
        payload.update({
            "id": new_test,
            "user_id": USER_ID,
            "visit_id": VISIT_ID,
            "assessment_id": new_test,
            "client_id": CLIENT_ID,
            "company_id": COMPANY_ID,
            "transcribe_type": TRANSCRIBE_TYPE,
            "mode": MODE
        })
        url = f"{BASE_URL}/transcription-request"
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json"
        }
        try:
            response = requests.post(url, json=payload, headers=headers)
            assert response.status_code == 200, f"Unexpected status code: {response.status_code}, body: {response.text}"
            data = response.json()
            assert "id" in data
            assert data["id"] == payload["id"]
            assert data["user_id"] == payload["user_id"]
            db.insert_item("results", f"{new_test}.json", data)
            all_results = db.get_item("results", "all-results.json")
            if not all_results:
                all_results = []
            all_results.append(data)
            db.insert_item("results", "all-results.json",  all_results)
            print(f"results: {data}")
            print(f"Test for {new_test}: PASSED")
        except AssertionError as e:
            print(f"Test for {new_test}: FAILED: {e}")
            failed = True
        except Exception as e:
            print(f"Test for {new_test}: ERROR: {e}")
            failed = True
    if failed:
        exit(1)
    print("All tests passed.")
