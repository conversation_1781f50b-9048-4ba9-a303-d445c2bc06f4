import { getAccessToken } from './getAccessToken';

// Helper to get base URL
function getBaseUrl() {
  const baseUrl = import.meta.env.VITE_BASE_URL;
  if (baseUrl) {
    return baseUrl.replace(/\/?$/, '/');
  } else {
    // Always use protocol://hostname:port/v1/
    const { protocol, hostname, port } = window.location;
    return `${protocol}//${hostname}${port ? `:${port}` : ''}/v1/`;
  }
}

// Simple global flag to prevent multiple redirects
let isRedirecting = false;

export async function apiClient<T = unknown>(
  endpoint: string,
  options: RequestInit = {},
  requireAuth: boolean = true
): Promise<T> {
  const url = getBaseUrl() + endpoint.replace(/^\//, '');
  let extraHeaders: Record<string, string> = {};
  if (options.headers && typeof options.headers === 'object' && !(options.headers instanceof Headers)) {
    extraHeaders = Object.fromEntries(Object.entries(options.headers).filter(([, v]) => typeof v === 'string'));
  }
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...extraHeaders,
  };
  if (requireAuth) {
    const token = getAccessToken();
    if (token) headers['Authorization'] = `Bearer ${token}`;
  }
  
  const response = await fetch(url, { ...options, headers });
  
  if (!response.ok) {
    // Handle 401 specifically - session expired
    if (response.status === 401 && !isRedirecting) {
      isRedirecting = true;
      
      // Clear all auth data
      localStorage.removeItem('adminUser');
      localStorage.removeItem('accessToken');
      
      // Redirect to signin
      window.location.href = '/signin';
      
      // Reset flag after redirect
      setTimeout(() => { isRedirecting = false; }, 1000);
    }
    
    const error = await response.text();
    throw new Error(error || 'API request failed');
  }
  
  return response.json();
}
