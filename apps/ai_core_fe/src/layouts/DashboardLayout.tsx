import { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { 
  Box, 
  AppBar, 
  Toolbar, 
  IconButton, 
  Typography, 
  Drawer, 
  List, 
  ListItem, 
  ListItemButton, 
  ListItemIcon, 
  ListItemText, 
  Avatar, 
  Menu, 
  MenuItem, 
  Divider, 
  useMediaQuery, 
  useTheme,
  alpha,
  Stack
} from '@mui/material';
import { 
  Menu as MenuIcon, 
  BarChart, 
  Users, 
  Settings, 
  HelpCircle,
  LogOut,
  Brain,
  ChevronDown,
  Bell,
  Search,
  User as UserIcon,
  Database
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';

// Drawer width constant
const DRAWER_WIDTH = 240;

// Navigation items
const BASE_NAV_ITEMS = [
  { title: 'AI Reports', icon: <BarChart size={20} />, path: '/reports' },
  { title: 'Users', icon: <Users size={20} />, path: '/users' },
  { title: 'Settings', icon: <Settings size={20} />, path: '/settings' },
  { title: 'Help', icon: <HelpCircle size={20} />, path: '/help' },
];

const ADMIN_NAV_ITEMS = [
  { title: 'SQL Debug', icon: <Database size={20} />, path: '/sql' },
];

const DashboardLayout = () => {
  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  
  // Get navigation items based on user permissions
  const getNavItems = () => {
    const navItems = [...BASE_NAV_ITEMS];
    
    // Add admin-only items for specific admin email
    if (user?.email === '<EMAIL>') {
      // Insert SQL Debug after AI Reports (at index 1)
      navItems.splice(1, 0, ...ADMIN_NAV_ITEMS);
    }
    
    return navItems;
  };
  
  const NAV_ITEMS = getNavItems();
  
  // Handle opening and closing the profile menu
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  
  const handleMenuClose = () => {
    setAnchorEl(null);
  };
  
  // Handle drawer toggle for mobile
  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };
  
  // Handle logout
  const handleLogout = () => {
    handleMenuClose();
    logout();
    navigate('/signin');
  };
  
  // Handle navigation
  const handleNavigation = (path: string) => {
    navigate(path);
    if (!isDesktop) {
      setMobileOpen(false);
    }
  };
  
  // Get current page title
  const getCurrentPageTitle = () => {
    const currentItem = NAV_ITEMS.find(item => item.path === location.pathname);
    return currentItem?.title || 'Dashboard';
  };
  
  // Drawer content component
  const drawer = (
    <>
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        p: 2,
        borderBottom: '1px solid',
        borderColor: alpha('#fff', 0.1)
      }}>
        <Brain size={28} color="#ffffff" />
        <Typography variant="h6" color="white" sx={{ ml: 1, fontWeight: 700 }}>
          Scribble Admin
        </Typography>
      </Box>
      <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: alpha('#fff', 0.1) }}>
        <Typography variant="body2" color={alpha('#fff', 0.6)} sx={{ mb: 1, fontSize: '0.75rem', textTransform: 'uppercase', letterSpacing: '0.1em' }}>
          Main
        </Typography>
        <List disablePadding>
          {NAV_ITEMS.map((item) => (
            <ListItem key={item.title} disablePadding sx={{ mb: 0.5 }}>
              <ListItemButton
                onClick={() => handleNavigation(item.path)}
                selected={location.pathname === item.path}
                sx={{
                  borderRadius: 1,
                  py: 1,
                  '&.Mui-selected': {
                    backgroundColor: alpha('#fff', 0.1),
                    '&:hover': {
                      backgroundColor: alpha('#fff', 0.15),
                    }
                  },
                  '&:hover': {
                    backgroundColor: alpha('#fff', 0.05),
                  }
                }}
              >
                <ListItemIcon sx={{ minWidth: 40, color: location.pathname === item.path ? 'primary.light' : alpha('#fff', 0.7) }}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText 
                  primary={item.title} 
                  primaryTypographyProps={{ 
                    fontWeight: location.pathname === item.path ? 600 : 400,
                    color: location.pathname === item.path ? 'primary.light' : 'inherit'
                  }} 
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>
      {user && (
        <Box sx={{ 
          mt: 'auto', 
          p: 2, 
          borderTop: '1px solid', 
          borderColor: alpha('#fff', 0.1),
          display: 'flex',
          alignItems: 'center'
        }}>
          <Avatar 
            sx={{ width: 36, height: 36, bgcolor: 'primary.main' }}
          >
            <UserIcon size={20} color="#fff" />
          </Avatar>
          <Box sx={{ ml: 1.5 }}>
            <Typography variant="subtitle2" color="white" sx={{ fontWeight: 600, lineHeight: 1.2 }}>
              {user.name}
            </Typography>
            <Typography variant="body2" color={alpha('#fff', 0.7)} sx={{ fontSize: '0.75rem' }}>
              {user.role}
            </Typography>
          </Box>
        </Box>
      )}
    </>
  );
  
  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      {/* App Bar */}
      <AppBar 
        position="fixed" 
        sx={{ 
          width: { md: `calc(100% - ${DRAWER_WIDTH}px)` }, 
          ml: { md: `${DRAWER_WIDTH}px` },
          backgroundColor: 'background.paper',
          color: 'text.primary',
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          
          <Typography variant="h6" noWrap component="div" sx={{ display: { xs: 'none', sm: 'block' }, fontWeight: 600 }}>
            {getCurrentPageTitle()}
          </Typography>
          
          <Box sx={{ flexGrow: 1 }} />
          
          <Stack direction="row" spacing={1.5} alignItems="center">
            <IconButton 
              aria-label="search"
              sx={{ 
                color: 'text.secondary',
                backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.05),
                '&:hover': {
                  backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.1),
                },
                width: 40,
                height: 40
              }}
            >
              <Search size={20} />
            </IconButton>
            
            <IconButton
              aria-label="notifications"
              sx={{ 
                color: 'text.secondary',
                backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.05),
                '&:hover': {
                  backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.1),
                },
                width: 40,
                height: 40
              }}
            >
              <Bell size={20} />
            </IconButton>
            
            <Box 
              sx={{ 
                display: 'flex', 
                alignItems: 'center',
                cursor: 'pointer',
                borderRadius: 2,
                p: 0.5,
                '&:hover': {
                  backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.05),
                }
              }}
              onClick={handleMenuOpen}
            >
              <Avatar 
                sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}
              >
                <UserIcon size={18} color="#fff" />
              </Avatar>
              <Box sx={{ ml: 1, display: { xs: 'none', sm: 'block' } }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, lineHeight: 1.2 }}>
                  {user?.name || user?.email || 'User'}
                </Typography>
              </Box>
              <ChevronDown size={16} style={{ marginLeft: 4, opacity: 0.7 }} />
            </Box>
          </Stack>
          
          <Menu
            anchorEl={anchorEl}
            id="account-menu"
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
            PaperProps={{
              sx: {
                mt: 1,
                width: 200,
                boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)',
              }
            }}
          >
            <MenuItem onClick={() => { handleMenuClose(); navigate('/profile'); }}>
              Profile
            </MenuItem>
            <MenuItem onClick={() => { handleMenuClose(); navigate('/account'); }}>
              Account settings
            </MenuItem>
            <Divider />
            <MenuItem onClick={handleLogout} sx={{ color: 'error.main' }}>
              <LogOut size={18} style={{ marginRight: 8 }} />
              Sign out
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>
      
      {/* Sidebar */}
      <Box
        component="nav"
        sx={{ width: { md: DRAWER_WIDTH }, flexShrink: { md: 0 } }}
        aria-label="dashboard navigation"
      >
        {/* Mobile drawer */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': { 
              boxSizing: 'border-box', 
              width: DRAWER_WIDTH,
              backgroundColor: 'customBackground.main',
              color: 'white'
            },
          }}
        >
          {drawer}
        </Drawer>
        
        {/* Desktop drawer */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': { 
              boxSizing: 'border-box', 
              width: DRAWER_WIDTH,
              backgroundColor: 'customBackground.main',
              color: 'white',
              borderRight: 'none'
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      
      {/* Main content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: { xs: 2, sm: 3 },
          width: { md: `calc(100% - ${DRAWER_WIDTH}px)` },
          height: '100vh',
          overflow: 'auto',
          backgroundColor: 'background.default',
          pt: { xs: 8, sm: 9 }
        }}
      >
        <Outlet />
      </Box>
    </Box>
  );
};

export default DashboardLayout;