import { useState, useEffect } from 'react';
import { Box, Typography, Container, Paper, Table, TableCell, TableContainer, TableHead, TableRow, Button, TextField, Stack, CircularProgress, Dialog, DialogTitle, DialogContent, DialogActions, IconButton } from '@mui/material';
import { UserPlus, Trash2, Eye, EyeOff } from 'lucide-react';
import { apiClient } from '../apiClient';

interface User {
  id: string;
  name: string;
  email: string;
}

// Helper to hash password with SHA-256
async function hashPassword(password: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(password);
  const hashBuffer = await window.crypto.subtle.digest('SHA-256', data);
  return Array.from(new Uint8Array(hashBuffer))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

const UsersPage = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [addName, setAddName] = useState('');
  const [addEmail, setAddEmail] = useState('');
  const [addPassword, setAddPassword] = useState('');
  const [addConfirmPassword, setAddConfirmPassword] = useState('');
  const [addShowPassword, setAddShowPassword] = useState(false);
  const [addShowConfirmPassword, setAddShowConfirmPassword] = useState(false);
  const [addError, setAddError] = useState('');
  const [addLoading, setAddLoading] = useState(false);

  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      setError('');
      try {
        const data = await apiClient<User[]>('user-list', { method: 'GET' }, true);
        setUsers(data);
      } catch (error) {
        console.error('Failed to fetch users:', error);
        setError('Failed to fetch users');
        setUsers([]);
      } finally {
        setLoading(false);
      }
    };
    fetchUsers();
  }, []);

  const handleAddUserDialogOpen = () => {
    setAddDialogOpen(true);
    setAddName('');
    setAddEmail('');
    setAddPassword('');
    setAddConfirmPassword('');
    setAddError('');
  };

  const handleAddUserDialogClose = () => {
    setAddDialogOpen(false);
  };

  const handleAddUser = async () => {
    if (!addName.trim() || !addEmail.trim() || !addPassword.trim() || !addConfirmPassword.trim()) {
      setAddError('All fields are required.');
      return;
    }
    if (addPassword !== addConfirmPassword) {
      setAddError('Passwords do not match.');
      return;
    }
    setAddLoading(true);
    setAddError('');
    try {
      // Hash the password before sending
      const password_hash = await hashPassword(addPassword);
      // POST to /user endpoint
      const newUser = await apiClient<User>(
        'user',
        {
          method: 'POST',
          body: JSON.stringify({ id: addEmail, name: addName, email: addEmail, password_hash }),
          headers: { 'Content-Type': 'application/json' },
        },
        true
      );
      setUsers([...users, { ...newUser, id: newUser.email }]);
      setAddDialogOpen(false);
    } catch (error) {
      console.error('Failed to add user:', error);
      setAddError('Failed to add user.');
    } finally {
      setAddLoading(false);
    }
  };

  const handleDeleteUser = async (id: string) => {
    try {
      await apiClient(
        `user/${id}`,
        {
          method: 'DELETE',
        },
        true
      );
      setUsers(users.filter((user) => user.id !== id));
    } catch (error) {
      console.error('Failed to delete user:', error);
    }
  };

  return (
    <Container maxWidth="md" sx={{ py: 8 }}>
      <Paper elevation={1} sx={{ p: 4, borderRadius: 2 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 700 }}>
          Users
        </Typography>
        <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
          <Button
            variant="contained"
            startIcon={<UserPlus size={18} />}
            onClick={handleAddUserDialogOpen}
            sx={{ minWidth: 140 }}
          >
            Add User 
          </Button>
        </Stack>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Typography color="error" sx={{ mb: 2 }}>{error}</Typography>
        ) : (
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <tbody>
                {users.map((user) => (
                  <TableRow key={user.email}>
                    <TableCell>{user.name}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell align="right">
                      <Button color="error" onClick={() => handleDeleteUser(user.email)} startIcon={<Trash2 size={18} />}>
                        Delete
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </tbody>
            </Table>
          </TableContainer>
        )}
      </Paper>
      {/* Add User Dialog */}
      <Dialog open={addDialogOpen} onClose={handleAddUserDialogClose} maxWidth="xs" fullWidth>
        <DialogTitle>Add User</DialogTitle>
        <DialogContent>
          <Stack spacing={2} sx={{ mt: 1 }}>
            <TextField
              label="Name"
              value={addName}
              onChange={e => setAddName(e.target.value)}
              fullWidth
              autoFocus
            />
            <TextField
              label="Email"
              value={addEmail}
              onChange={e => setAddEmail(e.target.value)}
              fullWidth
              type="email"
            />
            <TextField
              label="Password"
              value={addPassword}
              onChange={e => setAddPassword(e.target.value)}
              type={addShowPassword ? 'text' : 'password'}
              fullWidth
              InputProps={{
                endAdornment: (
                  <IconButton onClick={() => setAddShowPassword(v => !v)} edge="end" size="small">
                    {addShowPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </IconButton>
                )
              }}
            />
            <TextField
              label="Confirm Password"
              value={addConfirmPassword}
              onChange={e => setAddConfirmPassword(e.target.value)}
              type={addShowConfirmPassword ? 'text' : 'password'}
              fullWidth
              InputProps={{
                endAdornment: (
                  <IconButton onClick={() => setAddShowConfirmPassword(v => !v)} edge="end" size="small">
                    {addShowConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </IconButton>
                )
              }}
            />
            {addError && <Typography color="error">{addError}</Typography>}
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleAddUserDialogClose} disabled={addLoading}>Cancel</Button>
          <Button variant="contained" onClick={handleAddUser} disabled={addLoading}>
            {addLoading ? <CircularProgress size={20} /> : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default UsersPage;
