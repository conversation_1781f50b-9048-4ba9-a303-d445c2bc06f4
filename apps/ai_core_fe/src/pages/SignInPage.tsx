import { useState, FormEvent, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  Box, 
  Container, 
  Typography, 
  TextField, 
  Button, 
  Paper, 
  Grid,
  InputAdornment,
  IconButton,
  Link,
  CircularProgress,
  alpha
} from '@mui/material';
import { Eye, EyeOff, Brain } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { apiClient } from '../apiClient';

const SignInPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { login, isAuthenticated, setAccessToken } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  
  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      // Redirect to the originally requested path if available
      const from = (location.state && location.state.from && location.state.from.pathname) ? location.state.from.pathname : '/reports';
      navigate(from);
    }
  }, [isAuthenticated, navigate, location.state]);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError('');

    // Basic validation
    if (!email.trim()) {
      setError('Email is required');
      return;
    }
    if (!password.trim()) {
      setError('Password is required');
      return;
    }

    try {
      setLoading(true);
      // POST login request using apiClient
      const data = await apiClient<{ access_token?: string }>(
        'login',
        {
          method: 'POST',
          body: JSON.stringify({ username: email, password }),
          headers: { 'Content-Type': 'application/json' },
        },
        false // login does not require auth
      );
      if (data.access_token) {
        if (setAccessToken) setAccessToken(data.access_token);
      }
      await login(email, password); // still call context login for local state
      // After login, redirect to originally requested path
      const from = (location.state && location.state.from && location.state.from.pathname) ? location.state.from.pathname : '/reports';
      navigate(from);
    } catch (err) {
      setError('Failed to sign in. Please check your credentials.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <Box 
      sx={{ 
        minHeight: '100vh',
        display: 'flex',
        backgroundColor: 'background.default',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Decorative background elements */}
      <Box 
        sx={{
          position: 'absolute',
          top: -100,
          right: -100,
          width: 300,
          height: 300,
          borderRadius: '50%',
          backgroundColor: alpha('#6366F1', 0.05),
          zIndex: 0
        }}
      />
      <Box 
        sx={{
          position: 'absolute',
          bottom: -150,
          left: -150,
          width: 400,
          height: 400,
          borderRadius: '50%',
          backgroundColor: alpha('#0EA5E9', 0.05),
          zIndex: 0
        }}
      />
      
      <Container maxWidth="md" sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', position: 'relative', zIndex: 1 }}>
        <Grid container spacing={0} sx={{ height: '100%' }}>
          <Grid item xs={12} md={6} sx={{ 
            display: { xs: 'none', md: 'flex' },
            flexDirection: 'column',
            justifyContent: 'center',
            p: 4
          }}>
            <Box sx={{ mb: 4, display: 'flex', alignItems: 'center' }}>
              <Brain size={40} color="#6366F1" />
              <Typography variant="h4" component="h1" sx={{ ml: 1, fontWeight: 700, color: 'primary.main' }}>
                Scribble Admin
              </Typography>
            </Box>
            <Typography variant="h3" component="h2" sx={{ mb: 2, fontWeight: 700 }}>
              Welcome back
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              Access your AI dashboard to view reports and manage your analytics.
            </Typography>
            <Box sx={{ 
              backgroundColor: alpha('#6366F1', 0.05), 
              p: 2, 
              borderRadius: 2,
              border: '1px solid',
              borderColor: alpha('#6366F1', 0.1)
            }}>
              <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
                "The AI reports have helped us understand our customers better and make data-driven decisions."
              </Typography>
              <Typography variant="subtitle2" sx={{ mt: 1, fontWeight: 600 }}>
                — Greg Perry, Software Engineer
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Paper 
              elevation={0}
              sx={{ 
                p: { xs: 3, sm: 5 }, 
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                borderRadius: { xs: 0, md: 2 }
              }}
            >
              <Box sx={{ mb: 4, display: { xs: 'flex', md: 'none' }, alignItems: 'center', justifyContent: 'center' }}>
                <Brain size={32} color="#6366F1" />
                <Typography variant="h5" component="h1" sx={{ ml: 1, fontWeight: 700, color: 'primary.main' }}>
                  Scribble Admin
                </Typography>
              </Box>
              
              <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 700 }}>
                Sign in
              </Typography>
              <Typography variant="body1" color="text.secondary" paragraph>
                Enter your credentials to access your dashboard
              </Typography>
              
              {error && (
                <Box sx={{ mb: 2, p: 1.5, backgroundColor: alpha('#EF4444', 0.05), borderRadius: 1, borderLeft: '3px solid', borderColor: 'error.main' }}>
                  <Typography color="error" variant="body2">
                    {error}
                  </Typography>
                </Box>
              )}
              
              <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  id="email"
                  label="Email Address"
                  name="email"
                  autoComplete="email"
                  autoFocus
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  sx={{ mb: 2 }}
                />
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  name="password"
                  label="Password"
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  autoComplete="current-password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                        >
                          {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  sx={{ mb: 3 }}
                />
                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  color="primary"
                  size="large"
                  disabled={loading}
                  sx={{ 
                    mt: 2, 
                    py: 1.5,
                    position: 'relative',
                    '&:hover': {
                      transform: 'translateY(-1px)',
                      transition: 'transform 0.2s'
                    }
                  }}
                >
                  {loading ? <CircularProgress size={24} color="inherit" /> : 'Sign In'}
                </Button>
                <Box sx={{ mt: 2, textAlign: 'center' }}>

                  {/* <Link href="#" variant="body2" sx={{ color: 'primary.main' }}>
                    Forgot password?
                  </Link> */}
                </Box>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default SignInPage;